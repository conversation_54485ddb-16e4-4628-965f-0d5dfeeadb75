import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";
import { SysConfig, commonApi } from "@/api";
import systemInfo from "@/assets/json/systemInfo.json";
import packageJson from "./../../../../package.json";
import { cloneDeep } from "lodash";

const name = "simple-config"; // 定义模块名称
/**  DictState */
export interface SysConfigState {
  /** 系统基本信息 */
  sysInfo: SysConfig.SysBaseConfig;

  paramInfo: SysConfig.ParamConfig;
}

const initialState: SysConfigState = {
  sysInfo: {
    SYS_NAME: "",
    SYS_LOGO: "",
    SYS_VERSION: "",
    WAVE_TOOL_PATH: "",
    SYS_COPYRIGHT: "",
    SYS_COPYRIGHT_URL: ""
  },
  paramInfo: {
    PARAM_REFRESH_TIME: 5000,
    VARI_REFRESH_TIME: 5000,
    STATE_REFRESH_TIME: 2000,
    REPORT_REFRESH_TIME: 2000
  }
};

/** 配置模块 */
export const useConfigStore = defineStore({
  id: name,
  state: (): SysConfigState => {
    return {
      sysInfo: {
        SYS_NAME: "",
        SYS_LOGO: "",
        SYS_VERSION: "",
        WAVE_TOOL_PATH: "",
        SYS_COPYRIGHT: "",
        SYS_COPYRIGHT_URL: ""
      },
      paramInfo: {
        PARAM_REFRESH_TIME: 5000,
        VARI_REFRESH_TIME: 5000,
        STATE_REFRESH_TIME: 2000,
        REPORT_REFRESH_TIME: 5000
      }
    };
  },
  getters: {
    sysBaseInfoGet: state => state.sysInfo,
    paramInfoGet: state => state.paramInfo
  },
  actions: {
    /**  设置系统基本信息 */
    async setSysBaseInfo() {
      /**  获取系统基本信息 */
      console.log(this.sysInfo);
      // 保存当前的 WAVE_TOOL_PATH 值
      const currentWaveToolPath = this.sysInfo.WAVE_TOOL_PATH;

      // 更新系统信息
      this.sysInfo = systemInfo.data;
      this.sysInfo.SYS_NAME = packageJson.name;
      this.sysInfo.SYS_VERSION = packageJson.version;

      // 恢复保存的 WAVE_TOOL_PATH 值，如果之前有设置的话
      if (currentWaveToolPath) {
        this.sysInfo.WAVE_TOOL_PATH = currentWaveToolPath;
      }

      return this.sysInfo;
    },
    /** 获取系统基本信息 */
    async getSysBaseInfo() {
      return this.setSysBaseInfo();
    },
    async resetValues(preserveFields = ["SYS_NAME", "SYS_LOGO", "SYS_VERSION", "SYS_COPYRIGHT", "SYS_COPYRIGHT_URL"]) {
      // 创建一个新的 state 对象，包含默认值
      const newState = cloneDeep(initialState);

      // 合并需要保留的字段
      preserveFields.forEach(field => {
        (newState.sysInfo[field] as unknown as any) = this.$state.sysInfo[field];
      });
      newState.paramInfo = this.$state.paramInfo;
      // 将新的 state 对象赋值给当前 state
      this.$state = newState;
    },
    /**  设置系统基本信息 */
    async setParamInfo() {
      this.paramInfo = systemInfo.paramInfo;
      return this.sysInfo;
    },
    /** 获取系统基本信息 */
    async getParamInfo() {
      return this.setParamInfo();
    },
    async resetParamValues(preserveFields = []) {
      // 创建一个新的 state 对象，包含默认值
      const newState = cloneDeep(initialState);

      // 合并需要保留的字段
      preserveFields.forEach(field => {
        (newState.paramInfo[field] as unknown as any) = this.$state.paramInfo[field];
      });
      newState.sysInfo = this.$state.sysInfo;
      // 将新的 state 对象赋值给当前 state
      this.$state = newState;
    },
    /** 获取租户列表 */
    async setTenantList() {
      const { data } = await commonApi.tenantList();
      this.tenantList = data;
      return data;
    },

    /** 保存系统配置 */
    async saveSysConfig() {
      try {
        // 直接使用 Pinia 的持久化存储，不再调用 API
        // Pinia 会自动将状态持久化到存储中

        // 触发状态更新，确保界面刷新
        const currentState = cloneDeep(this.sysInfo);
        this.sysInfo = currentState;

        return true;
      } catch (error) {
        console.error("保存系统配置失败:", error);
        throw error;
      }
    },

    /** 保存参数配置 */
    async saveParamConfig() {
      try {
        // 直接使用 Pinia 的持久化存储，不再调用 API
        // Pinia 会自动将状态持久化到存储中

        // 触发状态更新，确保界面刷新
        const currentState = cloneDeep(this.paramInfo);
        this.paramInfo = currentState;

        return true;
      } catch (error) {
        console.error("保存参数配置失败:", error);
        throw error;
      }
    }
  },
  persist: piniaPersistConfig(name)
});
