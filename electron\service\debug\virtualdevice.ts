"use strict";

import { logger } from "ee-core/log";
import { IECReq } from "../../interface/debug/request";
import { t } from "../../data/i18n/i18n";

/**
 * 虚拟化装置Service
 * 负责虚拟化装置参数的查询、修改等业务逻辑，使用模拟数据
 * <AUTHOR>
 * @class
 */
class VirtualDeviceService {
  // 模拟数据存储
  private simulatedData: Map<string, any[]> = new Map();

  constructor() {
    logger.info(`[VirtualDeviceService] 虚拟化装置服务初始化完成`);
    this.initSimulatedData();
  }

  /**
   * 初始化模拟数据
   */
  private initSimulatedData() {
    // 模拟量参数数据
    this.simulatedData.set("read_analoy_para", [
      {
        id: "ai_1",
        name: "AI_1",
        description: "模拟量输入1",
        amplitude: 85.6,
        phase: 30.5,
        isModified: false,
        originalAmplitude: 85.6,
        originalPhase: 30.5,
        index: 1
      },
      {
        id: "ai_2", 
        name: "AI_2",
        description: "模拟量输入2",
        amplitude: 120.3,
        phase: 45.2,
        isModified: true,
        originalAmplitude: 115.0,
        originalPhase: 40.0,
        index: 2
      },
      {
        id: "ai_3",
        name: "AI_3", 
        description: "模拟量输入3",
        amplitude: 95.8,
        phase: 60.1,
        isModified: false,
        originalAmplitude: 95.8,
        originalPhase: 60.1,
        index: 3
      }
    ]);

    // 开入量参数数据
    this.simulatedData.set("read_bi_para", [
      {
        id: "bi_1",
        name: "BI_1",
        description: "开入量1",
        amplitude: 1,
        originalAmplitude: 1,
        isModified: false,
        index: 1
      },
      {
        id: "bi_2",
        name: "BI_2", 
        description: "开入量2",
        amplitude: 0,
        originalAmplitude: 1,
        isModified: true,
        index: 2
      },
      {
        id: "bi_3",
        name: "BI_3",
        description: "开入量3", 
        amplitude: 1,
        originalAmplitude: 1,
        isModified: false,
        index: 3
      }
    ]);

    // 开出量参数数据
    this.simulatedData.set("read_bo_para", [
      {
        id: "bo_1",
        name: "BO_1",
        description: "开出量1",
        amplitude: 0,
        index: 1
      },
      {
        id: "bo_2",
        name: "BO_2",
        description: "开出量2", 
        amplitude: 1,
        index: 2
      },
      {
        id: "bo_3",
        name: "BO_3",
        description: "开出量3",
        amplitude: 0,
        index: 3
      }
    ]);

    // 故障量参数数据
    this.simulatedData.set("read_fault_para", [
      {
        id: "fault_1",
        name: "FAULT_1",
        description: "故障信号1",
        amplitude: 0,
        originalAmplitude: 0,
        isModified: false,
        index: 1
      },
      {
        id: "fault_2", 
        name: "FAULT_2",
        description: "故障信号2",
        amplitude: 1,
        originalAmplitude: 0,
        isModified: true,
        index: 2
      },
      {
        id: "fault_3",
        name: "FAULT_3",
        description: "故障信号3",
        amplitude: 0,
        originalAmplitude: 0,
        isModified: false,
        index: 3
      }
    ]);

    // LED参数数据
    this.simulatedData.set("read_led_para", [
      {
        id: "led_brightness",
        name: "LED_BRIGHTNESS",
        description: "LED亮度",
        value: 75,
        index: 1
      },
      {
        id: "led_color",
        name: "LED_COLOR",
        description: "LED颜色",
        value: "红色",
        index: 2
      },
      {
        id: "led_mode",
        name: "LED_MODE", 
        description: "LED模式",
        value: "闪烁",
        index: 3
      }
    ]);

    logger.info(`[VirtualDeviceService] 模拟数据初始化完成`);
  }

  /**
   * 获取虚拟化装置参数
   * @param req 请求体，包含cmdType等参数
   * @returns 虚拟化装置参数列表
   */
  async getVirtualParams(req: IECReq<any>): Promise<{ list: any[]; total: number }> {
    try {
      logger.info(
        `[VirtualDeviceService] getVirtualParams - 开始获取虚拟化装置参数:`,
        req.data
      );

      const { cmdType, pageNum = 1, pageSize = 10 } = req.data;
      
      // 获取对应类型的模拟数据
      const data = this.simulatedData.get(cmdType) || [];
      
      // 分页处理
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = data.slice(startIndex, endIndex);

      const result = {
        list: paginatedData,
        total: data.length
      };

      logger.info(
        `[VirtualDeviceService] getVirtualParams - 成功获取虚拟化装置参数，类型: ${cmdType}，数量: ${paginatedData.length}`
      );

      return result;
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] getVirtualParams - 获取虚拟化装置参数异常:`,
        error
      );
      throw error;
    }
  }

  /**
   * 更新虚拟化装置参数
   * @param req 请求体，包含cmdType和params等参数
   * @returns 更新结果
   */
  async updateVirtualParams(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[VirtualDeviceService] updateVirtualParams - 开始更新虚拟化装置参数:`,
        req.data
      );

      const { cmdType, params } = req.data;
      
      if (!params || !Array.isArray(params)) {
        throw new Error("参数格式错误，params必须是数组");
      }

      // 获取对应类型的模拟数据
      const data = this.simulatedData.get(cmdType) || [];
      
      // 更新参数
      params.forEach(updateParam => {
        const existingParam = data.find(item => item.id === updateParam.id);
        if (existingParam) {
          // 根据不同类型更新不同字段
          if (cmdType === "read_analoy_para") {
            if (updateParam.amplitude !== undefined) {
              existingParam.amplitude = updateParam.amplitude;
              existingParam.isModified = existingParam.amplitude !== existingParam.originalAmplitude;
            }
            if (updateParam.phase !== undefined) {
              existingParam.phase = updateParam.phase;
              existingParam.isModified = existingParam.phase !== existingParam.originalPhase;
            }
          } else if (cmdType === "read_bi_para" || cmdType === "read_fault_para") {
            if (updateParam.amplitude !== undefined) {
              existingParam.amplitude = updateParam.amplitude;
              existingParam.isModified = existingParam.amplitude !== existingParam.originalAmplitude;
            }
          } else if (cmdType === "read_led_para") {
            if (updateParam.value !== undefined) {
              existingParam.value = updateParam.value;
            }
          }
          
          logger.info(
            `[VirtualDeviceService] updateVirtualParams - 更新参数: ${existingParam.name}`,
            existingParam
          );
        } else {
          logger.warn(
            `[VirtualDeviceService] updateVirtualParams - 未找到参数: ${updateParam.id}`
          );
        }
      });

      // 更新存储的数据
      this.simulatedData.set(cmdType, data);

      logger.info(
        `[VirtualDeviceService] updateVirtualParams - 成功更新虚拟化装置参数，类型: ${cmdType}，更新数量: ${params.length}`
      );

      return true;
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] updateVirtualParams - 更新虚拟化装置参数异常:`,
        error
      );
      throw error;
    }
  }

  /**
   * 重置虚拟化装置参数到原始值
   * @param req 请求体，包含cmdType和参数ID列表
   * @returns 重置结果
   */
  async resetVirtualParams(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[VirtualDeviceService] resetVirtualParams - 开始重置虚拟化装置参数:`,
        req.data
      );

      const { cmdType, paramIds } = req.data;
      
      // 获取对应类型的模拟数据
      const data = this.simulatedData.get(cmdType) || [];
      
      // 重置参数到原始值
      data.forEach(item => {
        if (!paramIds || paramIds.includes(item.id)) {
          if (cmdType === "read_analoy_para") {
            item.amplitude = item.originalAmplitude;
            item.phase = item.originalPhase;
            item.isModified = false;
          } else if (cmdType === "read_bi_para" || cmdType === "read_fault_para") {
            item.amplitude = item.originalAmplitude;
            item.isModified = false;
          }
          
          logger.info(
            `[VirtualDeviceService] resetVirtualParams - 重置参数: ${item.name}`
          );
        }
      });

      // 更新存储的数据
      this.simulatedData.set(cmdType, data);

      logger.info(
        `[VirtualDeviceService] resetVirtualParams - 成功重置虚拟化装置参数，类型: ${cmdType}`
      );

      return true;
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] resetVirtualParams - 重置虚拟化装置参数异常:`,
        error
      );
      throw error;
    }
  }
}

VirtualDeviceService.toString = () => "[class VirtualDeviceService]";
const virtualDeviceService = new VirtualDeviceService();

export { VirtualDeviceService, virtualDeviceService };