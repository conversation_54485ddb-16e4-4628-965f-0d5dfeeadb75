<template>
  <div class="virtual-wave-replay-test">
    <div class="test-header">
      <h2>故障录波回放测试界面</h2>
      <p>这是一个简单的测试界面</p>
    </div>

    <div class="test-content">
      <el-card>
        <template #header>
          <span>测试功能</span>
        </template>
        <div class="test-buttons">
          <el-button type="primary" @click="handleTest">测试按钮</el-button>
          <el-button type="success" @click="showMessage">显示消息</el-button>
          <el-button type="warning" @click="clearData">清空数据</el-button>
        </div>
      </el-card>

      <el-card style="margin-top: 20px">
        <template #header>
          <span>测试数据</span>
        </template>
        <div class="test-data">
          <!-- <p>设备ID: {{ deviceId || "未指定" }}</p> -->
          <p>测试状态: {{ testStatus }}</p>
          <p>测试时间: {{ testTime }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";

const testStatus = ref("待测试");
const testTime = ref("--");

const handleTest = () => {
  testStatus.value = "测试中...";
  setTimeout(() => {
    testStatus.value = "测试完成";
    testTime.value = new Date().toLocaleString();
    ElMessage.success("测试完成");
  }, 1000);
};

const showMessage = () => {
  ElMessage.info("这是一个测试消息");
};

const clearData = () => {
  testStatus.value = "待测试";
  testTime.value = "--";
  ElMessage.warning("数据已清空");
};
</script>

<style scoped lang="scss">
.virtual-wave-replay-test {
  padding: 20px;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    p {
      color: #7f8c8d;
      margin: 0;
    }
  }

  .test-content {
    max-width: 600px;
    margin: 0 auto;

    .test-buttons {
      display: flex;
      gap: 10px;
      justify-content: center;
    }

    .test-data {
      p {
        margin: 10px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
