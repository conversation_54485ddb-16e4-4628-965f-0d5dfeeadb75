import { readFileSync, existsSync } from "fs";
import { <PERSON>rse<PERSON> } from "xml2js";

import { DataTypeTemplates, DebugInfoMenu } from "../interface/debug/debuginfo";
import IECCONSTANTS from "../data/debug/iecConstants";

import { SingleGlobalDeviceInfo } from "../data/debug/singleGlobalDeviceInfo";
import { UpadRcpClient } from "iec-upadrpc/dist/src/UpadRpcClient";
import {
  AssociateRequestData,
  FileDirRequestData,
  FileReadRequestData,
} from "iec-upadrpc/dist/src/data";
import { logger } from "ee-core/log";
import { t } from "../data/i18n/i18n";

export class XmlFileManager {
  /**
   * 解析tree_menu.xml文件
   * @param xmlContent XML文件内容
   * @returns DebugInfoMenu数组
   */
  public async parseTreeMenu(xmlContent: any): Promise<DebugInfoMenu[]> {
    const parseMenu = (menuNode: any): DebugInfoMenu => {
      const menu: DebugInfoMenu = {
        name: menuNode?.$?.name || "",
        desc: menuNode?.$?.desc || "",
        menus: [],
      };
      return menu;
    };

    try {
      const rootMenus = xmlContent.TREE_MENU?.Menu;
      if (!rootMenus) return [];
      return Array.isArray(rootMenus)
        ? rootMenus.map((menu: any) => parseMenu(menu))
        : [parseMenu(rootMenus)];
    } catch (error) {
      console.error(t("errors.errorParsingXml"), error);
      return [];
    }
  }
  private xmlFilePath: string;
  private cachedMd5: string | null = null;
  private treeMenuFilePath: string;

  private debugInfoPath: string = "/debug_info.xml";
  private shrPath: string = "/shr";
  constructor() {
    this.xmlFilePath = IECCONSTANTS.PATH_CFG + this.debugInfoPath;
    this.treeMenuFilePath = IECCONSTANTS.PATH_CFG + "/tree_menu.xml";
  }

  /**
   * 获取当前XML文件的MD5值
   */
  public getCurrentMd5(): string | null {
    return this.cachedMd5;
  }

  /**
   * 比较MD5值并更新文件路径
   * @param md5 要比较的MD5值
   * @param newFilePath 新的文件路径（可选）
   * @returns 如果MD5匹配返回true，否则返回false
   */
  public async compareAndUpdateMd5(
    md5: string,
    deviceId: string,
    newFilePath?: string
  ): Promise<boolean> {
    logger.info(
      `[XmlFileManager] compareAndUpdateMd5 - 装置: ${deviceId}, 新MD5: ${md5}, 缓存MD5: ${this.cachedMd5}`
    );

    if (this.cachedMd5 === md5) {
      logger.info(`[XmlFileManager] MD5匹配，使用缓存`);
      return true;
    }
    
    if (newFilePath) {
      this.xmlFilePath = newFilePath;
    }

    try {
      const xmlData = readFileSync(this.getFilePath(deviceId), {
        encoding: "utf-8",
      });
      const result = await new Parser({
        explicitArray: false,
      }).parseStringPromise(xmlData);
      if (result?.DebugInfo?.Header?.$?.md5) {
        logger.info(
          "[XmlFileManager] compareAndUpdateMd5 readFileSync",
          result?.DebugInfo?.Header?.$?.md5
        );
        this.cachedMd5 = result.DebugInfo.Header.$.md5;
        const isMatch = this.cachedMd5 === md5;
        logger.info(
          `[XmlFileManager] MD5比较结果: ${isMatch ? '匹配' : '不匹配'}`
        );
        return isMatch;
      } else {
        logger.warn(`[XmlFileManager] XML文件中找不到MD5信息`);
      }
    } catch (error) {
      logger.error(`[XmlFileManager] 读取XML文件失败:`, error);
      console.error(t("errors.failedToUpdateXmlFile"), error);
    }
    return false;
  }

  /**
   * 首次连接先从文件中获取DataTypeTemplates
   */
  public async putFirstTimeDataTypeTemplates(
    singleGlobalDeviceInfo: SingleGlobalDeviceInfo
  ) {
    try {
      const xmlData = readFileSync(this.xmlFilePath, { encoding: "utf-8" });
      const result = await new Parser({
        explicitArray: false,
      }).parseStringPromise(xmlData);
      this.transformDataTypeTemplates(
        result.DebugInfo.DataTypeTemplates || {},
        singleGlobalDeviceInfo
      );
    } catch (error) {
      console.error(t("errors.failedToUpdateXmlFile"), error);
    }
  }

  public transformDataTypeTemplates(
    templates: any,
    singleGlobalDeviceInfo?: SingleGlobalDeviceInfo
  ): DataTypeTemplates {
    let enumTypes = [] as any;
    if (templates?.EnumType) {
      // 处理EnumType为对象或数组的情况
      if (Array.isArray(templates.EnumType)) {
        enumTypes = templates.EnumType;
      } else {
        enumTypes = [templates.EnumType];
      }
    }
    const result = enumTypes.map((enumType: any) => {
      const valMap = new Map<string, string>();
      if (enumType.Val) {
        // 处理Val为对象或数组的情况
        if (Array.isArray(enumType.Val)) {
          enumType.Val.forEach((val: any) => {
            if (val.$ && val.$.ord && val._ != "") {
              valMap.set(val.$.ord.toString(), val._);
            }
          });
        } else if (
          enumType.Val.$ &&
          enumType.Val.$.ord &&
          enumType.Val._ != ""
        ) {
          valMap.set(enumType.Val.$.ord.toString(), enumType.Val._);
        }
      }
      // 更新全局EnumType
      if (singleGlobalDeviceInfo) {
        const enumTypesMap = singleGlobalDeviceInfo.enumTypes;
        enumTypesMap.set(enumType.$.id, valMap);
      }

      return {
        id: enumType.$.id || "",
        valMap,
      };
    });

    return {
      EnumType: result,
    };
  }

  /**
   * findElementByName
   */
  public findElementByName(array: DebugInfoMenu[], targetName) {
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (element.name == targetName) {
        return element;
      }
      if (element.menus && element.menus.length > 0) {
        const ele = this.findElementByName(element.menus, targetName);
        if (ele) {
          return ele;
        }
      }
    }
    return null;
  }

  public async setDebugInfoFilePath(
    id: string,
    upadRcpClient: UpadRcpClient,
    singleGlobalDeviceInfo: SingleGlobalDeviceInfo
  ): Promise<boolean> {
    // 先比较默认文件MD5码
    const md5CompareRes = await this.getCompareMd5Result(id, upadRcpClient);
    if (md5CompareRes) {
      singleGlobalDeviceInfo.compareMd5Result = true;
      return md5CompareRes;
    }

    singleGlobalDeviceInfo.compareMd5Result = false;
    const fileDirRequestRes = await this.readFileDirRequest(upadRcpClient);
    logger.info(
      "[XmlFileManager] setDebugInfoFilePath md5CompareRes",
      fileDirRequestRes
    );
    if (fileDirRequestRes) {
      const fileReqData: FileReadRequestData = {
        fileItems: [fileDirRequestRes],
        savePath: IECCONSTANTS.PATH_CONFIG + "/" + id,
        cb: () => {},
      };
      logger.info("readfile start");
      const readResult = await upadRcpClient.readFile(fileReqData);
      logger.info("readfile end", readResult);
      return readResult.isSuccess();
    }
    return false;
  }

  public async getCompareMd5Result(
    id: string,
    upadRcpClient: UpadRcpClient
  ): Promise<boolean> {
    // 协商测试
    const requestData: AssociateRequestData = {
      version: "1.0",
      user: "root",
      passwd: "123456",
    };
    const asociateResult = await upadRcpClient.associate(requestData);
    let md5 = "";
    if (asociateResult.isSuccess() && asociateResult.data) {
      md5 = asociateResult.data.md5;
    }
    logger.info("asociateResult.isSuccess()", asociateResult);
    const md5CompareRes = await this.compareAndUpdateMd5(md5, id);
    logger.info(
      "[XmlFileManager] setDebugInfoFilePath md5CompareRes",
      md5CompareRes
    );
    if (md5CompareRes) {
      return md5CompareRes;
    }
    return false;
  }

  private async readFileDirRequest(client: UpadRcpClient) {
    client.setRequestTimeout(30 * 1000);
    const requestData: FileDirRequestData = {
      pathName: this.shrPath,
    };
    const result = await client.readFileDir(requestData);
    for (const item of result.data.fileEntry) {
      if (item.fileName === "debug_info.xml") {
        return item;
      }
    }
    return undefined;
  }

  /**
   * 获取当前XML文件路径
   */
  public getFilePath(id: string): string {
    const currentDebugInfoXml =
      IECCONSTANTS.PATH_CONFIG + "/" + id + this.shrPath + this.debugInfoPath;
    if (existsSync(currentDebugInfoXml)) {
      return currentDebugInfoXml;
    }
    return this.xmlFilePath;
  }

  public getTreeMenuFilePath(): string {
    return this.treeMenuFilePath;
  }
}
