import { ReportParam } from "@/api";

export type LayoutType = "vertical" | "classic" | "transverse" | "columns";

export type AssemblySizeType = "large" | "default" | "small";

export type LanguageType = "zh" | "en" | "es" | "fr" | "ru";

/* GlobalState */
export interface GlobalState {
  layout: LayoutType;
  assemblySize: AssemblySizeType;
  maximize: boolean;
  primary: string;
  isDark: boolean;
  isGrey: boolean;
  isConsole: boolean;
  consoleHeight: number;
  isDeviceList: boolean;
  isFunctionList: boolean; // 新增，功能列表收起/展开
  isWeak: boolean;
  breadcrumb: boolean;
  breadcrumbIcon: boolean;
  tabs: boolean;
  tabsIcon: boolean;
  footer: boolean;
  drawerForm: boolean;
  watermark: boolean;
  language: LanguageType;
}

/* tabsMenuProps */
export interface TabsMenuProps {
  icon: string;
  title: string;
  path: string;
  name: string;
  close: boolean;
  isKeepAlive: boolean;
}

/* TabsState */
export interface TabsState {
  tabsMenuList: TabsMenuProps[];
}

/* KeepAliveState */
export interface KeepAliveState {
  keepAliveName: string[];
}

/* DebugIndex */
export interface DebugIndex {
  /** 子页面索引 */
  asideIndex: string;
  /** 组件 */
  currentComponent: Map<string, any>;
  /** 组件名 */
  compName: string;
  /** 页面数据 */
  compData: Map<string, any>;
}

export interface DebugDeviceInfo {
  id: string;
  ip: string;
  name: string;
  port: string;
  encrypted: boolean;
  isVirtual?: boolean; // 虚拟化装置
  prjType: number;
  deviceType: number;
  isConnect: boolean;
  isActive: boolean;
  connectTimeout?: number;
  paramTimeout?: number;
  readTimeout?: number;
  connectTime: string;
}

export interface DebugReportInfo {
  currReportType: string;
  currReportMethod: string;
  currReportDesc: string;
  newname: string; // 名称
  keyword: string; // 关键词
  isReportLoading: boolean;
  commonReport: Map<string, ReportParam.IECRpcCommonReportRes[]>;
  operateReport: Map<string, ReportParam.IECRpcOperateReportRes[]>;
  groupReport: Map<string, ReportParam.IECRpcGroupReportRes[]>;
  auditlogReport: Map<string, ReportParam.IECRpcAuditLogReportRes[]>;
  // 查询条件缓存，按报告类型存储
  queryConditions: Map<string, ReportQueryCondition>;
}

export interface ReportQueryCondition {
  isDateCheck: boolean;
  dateRange: [Date, Date];
  searchInfo: string;
  searchType: number;
}

export interface ToolInfo {
  // 索引
  asideIndex: string;
  tabIndex: string;
}
