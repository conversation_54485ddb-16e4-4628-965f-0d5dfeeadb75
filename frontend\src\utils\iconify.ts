import { addCollection } from "@iconify/vue";
import antJson from "@iconify/json/json/ant-design.json";
import epJson from "@iconify/json/json/ep.json";
import etJson from "@iconify/json/json/et.json";
import evaJson from "@iconify/json/json/eva.json";
import flatJson from "@iconify/json/json/flat-color-icons.json";
import lineMdJson from "@iconify/json/json/line-md.json";
// 类型断言解决 twemoji.json 导入问题
import twemojiJsonRaw from "@iconify/json/json/twemoji.json";
import type { IconifyJSON } from "@iconify/types";

export async function downloadAndInstall() {
  try {
    // 添加常用图标集合
    addCollection(antJson);
    addCollection(epJson);
    addCollection(etJson);
    addCollection(evaJson); // 确保 eva 图标集合被加载
    addCollection(flatJson);
    addCollection(lineMdJson);
    // 类型断言确保 twemojiJson 符合 IconifyJSON 接口
    addCollection(twemojiJsonRaw as IconifyJSON);

    console.log("✅ Iconify 图标集合加载成功");
  } catch (error) {
    console.error("❌ Iconify 图标集合加载失败:", error);
  }
}
