<template>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("report.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-text type="primary">{{ t("report.total", { num: totalNum }) }}</el-text>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{ t("report.search") }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{ t("report.save") }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("report.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table">
      <ProTable
        ref="proTable"
        :data="tableData"
        :columns="proTableColumns"
        :pagination="true"
        :tool-button="false"
        :border="true"
        :stripe="true"
        :loading="tableLoad"
        :pager-count="7"
        :request-auto="false"
        row-key="entryID"
      >
        <template #tableHeader>
          <span></span>
        </template>
      </ProTable>
    </div>
  </div>
  <el-dialog
    v-model="showDialog"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('report.progress')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
  </el-dialog>
  <ProgressDialog ref="progressDialog" />
</template>
<script setup lang="ts">
import { Delete, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { ipc } from "@/api/request/ipcRenderer";
import { getDateZh } from "@/utils/index";
import { IECNotify, reportApi, ReportParam, ResultData } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";

import { osControlApi } from "@/api/modules/biz/os";
import { ref, reactive, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessageBox } from "element-plus";
import ProgressDialog from "../dialog/ProgressDialog.vue";

const { t } = useI18n();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const { report, addConsole } = useDebugStore();
const globalReport = report.get(props.deviceId);

// 初始化查询条件
const getDefaultDateRange = (): [Date, Date] => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return [yesterday, new Date()];
};

const dateRange = ref<[Date, Date]>(getDefaultDateRange());
const isDateCheck = ref<boolean>(false);
const isButtonClick = ref<boolean>(false);
const tableLoad = ref<boolean>(false);
const tableData = ref<ReportParam.IECRpcAuditLogReportRes[]>([]);
const totalNum = computed(() => {
  return tableData.value.length;
});

const dialogShow = reactive({
  percentage: 0,
  progressText: ""
});
const showDialog = ref(false);
const progressDialog = ref();
const getTableData = (): ReportParam.IECRpcAuditLogReportRes[] => {
  return globalReport!.auditlogReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
};

// ProTable列配置
const proTableColumns: ColumnProps<ReportParam.IECRpcAuditLogReportRes>[] = [
  { prop: "entryID", label: t("report.reportNumber"), width: 100 },
  { prop: "time", label: t("report.time"), width: 200 },
  { prop: "module", label: t("report.module"), width: 120 },
  { prop: "type", label: t("report.type"), width: 100 },
  { prop: "level", label: t("report.level"), width: 100 },
  { prop: "user", label: t("report.user"), width: 120 },
  { prop: "result", label: t("report.result"), width: 100 },
  { prop: "orig", label: t("report.origin"), width: 120 },
  { prop: "msg", label: t("report.msg"), minWidth: 200, align: "left" }
];

// 保存查询条件到缓存
const saveQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  globalReport.queryConditions.set(reportKey, {
    isDateCheck: isDateCheck.value,
    dateRange: dateRange.value && dateRange.value.length >= 2 ? [new Date(dateRange.value[0]), new Date(dateRange.value[1])] : getDefaultDateRange(),
    searchInfo: "",
    searchType: 0
  });
};

// 从缓存恢复查询条件
const restoreQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  const cached = globalReport.queryConditions.get(reportKey);
  if (cached) {
    isDateCheck.value = cached.isDateCheck;
    dateRange.value = [new Date(cached.dateRange[0]), new Date(cached.dateRange[1])];
  } else {
    // 如果没有缓存，使用默认值
    isDateCheck.value = false;
    dateRange.value = getDefaultDateRange();
  }
};
let searchStartTime = 0; // 记录搜索开始时间
let batchRenderTimer: any = null; // 分批渲染定时器

const clearList = (): void => {
  globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
  // 清理分批渲染定时器
  if (batchRenderTimer) {
    clearTimeout(batchRenderTimer);
    batchRenderTimer = null;
  }
};

// 强制结束加载状态的函数
const forceEndLoading = (reason: string): void => {
  console.log(`[AuditLog UI] 强制结束加载状态 - 原因: ${reason}`);
  tableLoad.value = false;
  isButtonClick.value = false;
  dialogShow.percentage = 100;
  showDialog.value = false;

  nextTick(() => {
    console.log(`[AuditLog UI] 强制结束完成 - tableLoad: ${tableLoad.value}, showDialog: ${showDialog.value}`);
  });
};

// 注意：现在不再需要分批渲染，因为只在最终完成时更新表格数据

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("report.selectCompleteTimeRange"));
    return;
  }

  // 保存查询条件到缓存
  saveQueryConditions();

  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[0])) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[1])) : ""
  };
  globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableLoad.value = true;
  isButtonClick.value = true;
  dialogShow.percentage = 0;
  showDialog.value = true; // 弹窗立即显示
  const reportDesc: string = globalReport!.currReportDesc;
  dialogShow.progressText = t("report.querying") + reportDesc;

  // 记录搜索开始时间
  searchStartTime = Date.now();

  // 调用API，进度将通过回调通知更新
  const res: ResultData<any> = await reportApi.getAuditReportListByDevice(props.deviceId, arg);

  console.log(`[AuditLog UI] API调用完成 - code: ${res.code}, 耗时: ${Date.now() - searchStartTime}ms`);

  // API调用完成后，强制确保界面状态正确
  if (res.code === 1) {
    // 成功情况：延迟一点时间确保最后的通知处理完成，然后强制结束加载状态
    setTimeout(() => {
      if (tableLoad.value || showDialog.value) {
        forceEndLoading("API成功完成，超时保护触发");
      }
    }, 500); // 500ms后强制结束，确保通知处理完成
  } else {
    // 失败情况：立即结束加载状态
    forceEndLoading("API调用失败");
    Message.warning(res.msg);
    return;
  }

  // 添加超时保护，防止界面一直卡住
  // setTimeout(() => {
  //   if (tableLoad.value) {
  //     console.warn(`[AuditLog UI] 检测到界面可能卡住，强制结束加载状态`);
  //     tableLoad.value = false;
  //     isButtonClick.value = false;
  //     dialogShow.percentage = 100;
  //     showDialog.value = false;
  //     Message.warning("查询超时，请重试");
  //   }
  // }, 5000); // 5秒超时保护
};

const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.noDataToSave") }));
    return;
  }

  // 第一步：先选择保存路径，不显示进度条
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [
      { name: "Rpt", extensions: ["rpt"] },
      { name: "Excel", extensions: ["xlsx"] }
    ]
  });

  // 如果用户取消选择路径，直接返回，不显示任何错误信息
  if (!path) {
    addConsole(t("report.exportLogCancelled"));
    return;
  }

  // 第二步：用户确认路径后，才显示进度条并开始导出
  progressDialog.value.show();
  const exportList: any[] = [];
  exportList;
  getTableData().forEach(obj => {
    exportList.push({
      entryID: obj.entryID,
      module: obj.module,
      msg: obj.msg,
      type: obj.type,
      level: obj.level,
      orig: obj.orig,
      time: obj.time,
      result: obj.result,
      user: obj.user
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: exportList
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  progressDialog.value.hide();
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm") || "确定",
    type: "success"
  }).catch(() => {
    // 用户点击关闭按钮取消操作，不需要处理
  });
};

const initTableData = (): void => {
  if (getTableData().length > 0) {
    dialogShow.percentage = 50;
    showDialog.value = true;
    dialogShow.progressText = t("report.loadingText");
  }
  nextTick(() => {
    setTimeout(() => {
      tableData.value = getTableData();
      dialogShow.percentage = 100;
      showDialog.value = false;
    }, 50);
  });
};

ipc.on("report_notify", (_event: unknown, notify: IECNotify) => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;

  const uiUpdateStartTime = Date.now();
  // console.log(`[AuditLog UI] 收到通知开始处理 - 时间: ${new Date(uiUpdateStartTime).toISOString()}, isPartial: ${notify.isPartial}, type: ${notify.type}`);

  const reportData = notify.data as any;
  if (notify.type == "readAuditLogReport") {
    console.log(
      `[AuditLog UI] 处理审计报告通知 - isPartial: ${notify.isPartial}, 数据类型: ${reportData.data?.progress ? "进度" : "数据"}, 当前界面状态: tableLoad=${tableLoad.value}, showDialog=${showDialog.value}`
    );

    if (reportData.code != 1) {
      forceEndLoading("处理错误");
      Message.warning(reportData.msg);
      console.log(`[AuditLog UI] 处理错误完成 - 耗时: ${Date.now() - uiUpdateStartTime}ms`);
      return;
    }

    // 检查是否为进度更新
    if (reportData.data?.progress?.isProgress) {
      // 这是进度更新，只更新进度条，不更新表格数据
      const progressInfo = reportData.data.progress;
      const currentCount = progressInfo.currentCount;
      const callbackCount = progressInfo.callbackCount;

      // 基于实际数据量计算进度
      let progressPercent = 0;
      if (currentCount > 0) {
        // 使用对数函数计算进度，避免进度跳跃过快
        progressPercent = Math.min(85, Math.floor(Math.log(currentCount + 1) * 10 + callbackCount * 2));
      } else {
        progressPercent = Math.min(20, callbackCount * 3);
      }

      // 确保进度不倒退
      progressPercent = Math.max(dialogShow.percentage, progressPercent);

      dialogShow.percentage = progressPercent;
      dialogShow.progressText = t("report.querying") + globalReport!.currReportDesc + ` (${currentCount} ${t("report.items")})`;

      console.log(
        `[AuditLog UI] 进度更新完成 - 数据量: ${currentCount}条, 回调次数: ${callbackCount}, 进度: ${progressPercent}%, 耗时: ${Date.now() - uiUpdateStartTime}ms`
      );
      return;
    }

    // 这是最终数据更新
    if (Array.isArray(reportData.data)) {
      console.log(`[AuditLog UI] 处理最终数据 - 原始数据量: ${reportData.data.length}条`);

      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        const filterStartTime = Date.now();
        filteredList = reportData.data.filter((item: any) => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
        console.log(
          `[AuditLog UI] 数据过滤耗时: ${Date.now() - filterStartTime}ms, 原始数据: ${reportData.data.length}条, 过滤后: ${filteredList.length}条`
        );
      }

      const domUpdateStartTime = Date.now();
      // 最终数据直接渲染到表格
      tableData.value = filteredList;
      globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);
      const domUpdateTime = Date.now() - domUpdateStartTime;

      // 使用强制结束函数完成加载
      forceEndLoading(`最终数据处理完成 - 数据量: ${filteredList.length}条`);

      console.log(
        `[AuditLog UI] 最终数据处理完成 - DOM更新: ${domUpdateTime}ms, 总耗时: ${Date.now() - uiUpdateStartTime}ms, 最终数据量: ${filteredList.length}条`
      );
    } else {
      // 空数据情况
      console.log(`[AuditLog UI] 处理空数据情况`);
      globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];
      forceEndLoading("空数据处理完成");
      console.log(`[AuditLog UI] 空数据处理完成 - 总耗时: ${Date.now() - uiUpdateStartTime}ms`);
    }
  }
});

onMounted(() => {
  initTableData();
  // 恢复查询条件
  restoreQueryConditions();
});
watch(
  () => globalReport!.newname,
  () => {
    tableData.value = getTableData();
    isButtonClick.value = false;

    // 恢复查询条件而不是重置
    restoreQueryConditions();
  }
);
onBeforeUnmount(() => {
  ipc.removeAllListeners("report_notify");
});
</script>
<style scoped lang="scss">
.report-page {
  margin-top: 5px;
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
    .el-row {
      div {
        font-size: 14px;
        :deep(.el-date-editor) {
          width: 340px;
        }
      }
    }
  }
  .report-table {
    overflow-y: auto;
    scrollbar-width: none;
    height: calc(100vh - 220px);
    min-height: 400px;
  }
}
.header {
  margin-bottom: 5px;
}
.no-warp-cell .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
</style>
