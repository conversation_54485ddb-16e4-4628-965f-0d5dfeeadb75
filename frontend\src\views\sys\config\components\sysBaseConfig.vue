<template>
  <div class="card">
    <el-form ref="sysConfigFormRef" :model="sysInfo" label-width="auto" style="max-width: 600px" label-suffix=" :">
      <!-- <el-row :gutter="16">
        <el-col :span="8"> -->
      <s-form-item :label="t('sys.config.systemName')" prop="SYS_NAME">
        <s-input v-model="sysInfo.SYS_NAME" :readonly="true"></s-input>
      </s-form-item>
      <!-- </el-col>
        <el-col :span="8"> -->
      <s-form-item :label="t('sys.config.systemVersion')" prop="SYS_VERSION">
        <s-input v-model="sysInfo.SYS_VERSION" :readonly="true"></s-input>
      </s-form-item>
      <!-- </el-col>
      </el-row> -->
      <!-- <el-row :gutter="16">
        <el-col :span="16"> -->
      <s-form-item :label="t('sys.config.waveToolPath')" prop="WAVE_TOOL_PATH">
        <el-input v-model="sysInfo.WAVE_TOOL_PATH" :placeholder="t('sys.config.waveToolPathPlaceholder')" :readonly="true">
          <template #append>
            <el-tooltip
              :content="t('sys.config.openDirectory')"
              popper-class="is-small"
              transition="none"
              effect="light"
              placement="right-start"
              :show-after="0"
              :hide-after="0"
            >
              <el-button :icon="Folder" @click="openDirectory" />
            </el-tooltip>
          </template>
        </el-input>
      </s-form-item>
      <!-- </el-col>
      </el-row> -->
      <!-- <el-row :gutter="16">
        <el-col :span="24"> -->
      <el-form-item>
        <el-button type="primary" :loading="submitLoading" @click="onSubmit()">{{ t("sys.config.save") }}</el-button>
        <el-button style="margin-left: 10px" @click="resetForm">{{ t("sys.config.reset") }}</el-button>
      </el-form-item>
      <!-- </el-col>
      </el-row> -->
    </el-form>
  </div>
</template>

<script setup lang="ts">
import Message from "@/scripts/message";
import { useConfigStore } from "@/stores/modules/config";
import { FormInstance } from "element-plus";
import { storeToRefs } from "pinia";
import { Folder } from "@element-plus/icons-vue";
import { osControlApi } from "@/api/modules/biz/os";
import { isEmpty } from "lodash";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const configStore = useConfigStore();
const { sysInfo } = storeToRefs(configStore);
const submitLoading = ref(false); //提交按钮loading
const sysConfigFormRef = ref<FormInstance>(); //表单实例

const openDirectory = async (): Promise<void> => {
  const res: any = await osControlApi.selectFileByParams({
    title: t("sys.config.selectWaveTool"),
    filterList: [{ name: "exe", extensions: ["exe"] }]
  });
  const path = res.path;
  if (!isEmpty(path)) {
    sysInfo.value.WAVE_TOOL_PATH = path;
  }
};

/** 提交表单 */
async function onSubmit() {
  submitLoading.value = true;
  try {
    // 调用保存配置的API
    await configStore.saveSysConfig();
    // 强制刷新界面数据
    sysInfo.value = { ...sysInfo.value };
    Message.success(t("sys.config.saveSuccess"));
  } catch (error) {
    console.error("保存系统配置失败:", error);
    Message.error(t("sys.config.saveFailed"));
  } finally {
    submitLoading.value = false;
  }
}

/** 重置表单 */
function resetForm() {
  configStore.resetValues();
  sysConfigFormRef.value?.resetFields();
}
</script>

<style lang="scss" scoped></style>
