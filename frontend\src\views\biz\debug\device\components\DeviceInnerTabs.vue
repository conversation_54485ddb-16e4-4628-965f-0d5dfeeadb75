<template>
  <div class="device-inner-tabs">
    <el-tabs v-model="activeTab" @tab-click="tabClick" lazy>
      <el-tab-pane :name="t('device.innerTabs.contentView')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Management /></el-icon>
            <span>{{ tabLabel }}</span>
          </span>
        </template>
        <div style="height: 100%">
          <component :is="currentComponent" :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <!-- 虚拟化装置隐藏文件上传功能 -->
      <el-tab-pane v-if="!isVirtualDevice" :name="t('device.innerTabs.fileUpload')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Upload /></el-icon>
            <span>{{ t("device.innerTabs.fileUpload") }}</span>
          </span>
        </template>
        <div v-if="shouldRenderTab('fileUpload')" style="height: 100%">
          <FileUpload :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <!-- 虚拟化装置隐藏文件下载功能 -->
      <el-tab-pane v-if="!isVirtualDevice" :name="t('device.innerTabs.fileDownload')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Download /></el-icon>
            <span>{{ t("device.innerTabs.fileDownload") }}</span>
          </span>
        </template>
        <div v-if="shouldRenderTab('fileDownload')" style="height: 100%">
          <FileDownload :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <!-- 虚拟化装置隐藏装置对时功能 -->
      <el-tab-pane v-if="!isVirtualDevice" :name="t('device.innerTabs.deviceTime')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Calendar /></el-icon>
            <span>{{ t("device.innerTabs.deviceTime") }}</span>
          </span>
        </template>
        <div v-if="shouldRenderTab('deviceTime')" style="height: 100%">
          <DeviceTime :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <!-- 虚拟化装置隐藏装置操作功能 -->
      <el-tab-pane v-if="!isVirtualDevice" :name="t('device.innerTabs.deviceOperate')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Setting /></el-icon>
            <span>{{ t("device.innerTabs.deviceOperate") }}</span>
          </span>
        </template>
        <div v-if="shouldRenderTab('deviceOperate')" style="height: 100%">
          <DeviceOperate :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <!-- 虚拟化装置隐藏变量调试功能 -->
      <el-tab-pane v-if="!isVirtualDevice" :name="t('device.innerTabs.variableDebug')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><ElemeFilled /></el-icon>
            <span>{{ t("device.innerTabs.variableDebug") }}</span>
          </span>
        </template>
        <div v-if="shouldRenderTab('variableDebug')" style="height: 100%">
          <DebugVariable :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <!-- 虚拟化装置隐藏一键备份功能 -->
      <el-tab-pane v-if="!isVirtualDevice" :name="t('device.innerTabs.oneClickBackup')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Collection /></el-icon>
            <span>{{ t("device.innerTabs.oneClickBackup") }}</span>
          </span>
        </template>
        <div v-if="shouldRenderTab('oneClickBackup')" style="height: 100%">
          <DeviceBackUp :device-id="deviceId" />
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="false" :name="t('device.innerTabs.entryConfig')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Coin /></el-icon>
            <span>{{ t("device.innerTabs.entryConfig") }}</span>
          </span>
        </template>
        <DictCfg :device-id="deviceId"></DictCfg>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { TabsPaneContext } from "element-plus";
import { watch, ref, shallowRef, computed } from "vue";
import { useDebugStore } from "@/stores/modules/debug";
import { Management, Upload, Download, Calendar, Setting, ElemeFilled, Collection, Coin } from "@element-plus/icons-vue";
import DebugVariable from "../views/DebugVariable.vue";
import DeviceTime from "../views/DeviceTime.vue";
import DeviceSummary from "../views/DeviceSummary.vue";
import DeviceOperate from "../views/DeviceOperate.vue";
import FileUpload from "../views/FileUpload.vue";
import FileDownload from "../views/FileDownload.vue";
import DictCfg from "../views/DictCfg.vue";
import DeviceBackUp from "../views/DeviceBackUp.vue";
import { useI18n } from "vue-i18n";
import { DebugDeviceInfo } from "@/stores/interface";

const currentComponent = shallowRef(DeviceSummary);
const componentParam = ref({});
const { debugIndex } = useDebugStore();
const { t, locale } = useI18n();
const activeTab = ref(t("device.innerTabs.contentView"));
const tabLabel = ref(t("device.innerTabs.contentView"));

// 已渲染的标签页缓存
const renderedTabs = ref(new Set(["contentView"]));

const props = defineProps<{
  deviceId: string;
  deviceModel: DebugDeviceInfo;
}>();

// 计算属性：判断是否为虚拟化装置
const isVirtualDevice = computed(() => {
  return props.deviceModel?.isVirtual === true;
});

// 判断是否应该渲染标签页
const shouldRenderTab = (tabName: string) => {
  const currentTabName = getCurrentTabName();
  if (currentTabName === tabName) {
    renderedTabs.value.add(tabName);
    return true;
  }
  return renderedTabs.value.has(tabName);
};

// 获取当前标签页名称
const getCurrentTabName = () => {
  if (activeTab.value === t("device.innerTabs.fileUpload")) return "fileUpload";
  if (activeTab.value === t("device.innerTabs.fileDownload")) return "fileDownload";
  if (activeTab.value === t("device.innerTabs.deviceTime")) return "deviceTime";
  if (activeTab.value === t("device.innerTabs.deviceOperate")) return "deviceOperate";
  if (activeTab.value === t("device.innerTabs.variableDebug")) return "variableDebug";
  if (activeTab.value === t("device.innerTabs.oneClickBackup")) return "oneClickBackup";
  if (activeTab.value === t("device.innerTabs.entryConfig")) return "entryConfig";
  return "contentView";
};

// 监听语言切换，更新 activeTab 和 tabLabel
watch(locale, () => {
  // 只有当前是默认的内容视图时才更新
  if (activeTab.value === t("device.innerTabs.contentView") || tabLabel.value === t("device.innerTabs.contentView")) {
    activeTab.value = t("device.innerTabs.contentView");
    tabLabel.value = t("device.innerTabs.contentView");
  }
});
const tabClick = (tabItem: TabsPaneContext) => {
  const fullPath = tabItem.props.name as string;
  console.log(t("device.innerTabs.tabClickLog") + fullPath);
};
const changeTab = (tabName: string, label: string) => {
  activeTab.value = tabName;
  tabLabel.value = label;
};

const changeComponent = async () => {
  const id = props.deviceId;
  if (id) {
    const comp = debugIndex.currentComponent.get(id);
    const param = debugIndex.compData;
    if (comp != null) {
      currentComponent.value = comp;
      componentParam.value = param;
    }
  }
};

defineExpose({
  changeTab,
  changeComponent
});
</script>

<style scoped lang="scss">
.device-inner-tabs {
  width: 100%;
  height: 100%;
  margin-left: 8px;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs) {
    height: 100%;
    .el-tab-pane {
      height: 100%;
    }
  }
}
.device-inner-tabs > .el-tabs__content {
  padding: 32px;
  font-size: 32px;
  font-weight: 600;
  color: #6b778c;
}
.device-inner-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}
.device-inner-tabs .custom-tabs-label span {
  margin-left: 4px;
  vertical-align: middle;
}
</style>
