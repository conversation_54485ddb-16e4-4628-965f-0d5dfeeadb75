<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item @click="sameSearch">
      <svg-icon icon="ant-design:search-outlined" class="menu-svg" />
      <span>{{ t("report.sameSearch") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-item @click="sameFilter">
      <svg-icon icon="ant-design:filter-outlined" class="menu-svg" />
      <span>{{ t("report.sameFilter") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="dealTimeCol">
      <svg-icon icon="ant-design:field-time-outlined" class="menu-svg" />
      <span>{{ t("report.showHideTime") }}</span>
    </v-contextmenu-item>
  </v-contextmenu>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("report.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-text type="primary">{{ t("report.total", { num: totalNum }) }}</el-text>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{ t("report.search") }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{ t("report.save") }}</el-button>
        <el-button type="primary" plain :icon="Refresh" @click="refreshReport" :loading="refreshLoading">{{ refreshName }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("report.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table" v-contextmenu:contextmenu="contextmenu">
      <ProTable
        ref="proTable"
        :data="tableData"
        :columns="proTableColumns"
        :pagination="true"
        :tool-button="false"
        :border="true"
        :stripe="true"
        :loading="tableLoad"
        :pager-count="7"
        :request-auto="false"
        row-key="entryID"
        @cell-contextmenu="cellContextmenu"
      >
        <template #tableHeader>
          <span></span>
        </template>
      </ProTable>
    </div>
  </div>
  <el-dialog
    v-model="dialogShow.searchProgress"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('report.progress')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
  </el-dialog>
  <ProgressDialog ref="progressDialog" />
</template>
<script setup lang="ts">
import { Delete, Refresh, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { ipc } from "@/api/request/ipcRenderer";
import { getDateZh } from "@/utils/index";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { IECNotify, RealEventState, ReportParam, ResultData } from "@/api";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { ref, computed } from "vue";
import { useDebugStore } from "@/stores/modules/debug";
import { reportApi } from "@/api/modules/biz/debug/report";
import { realEventApi } from "@/api/modules/biz/debug/realevent";
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
import { osControlApi } from "@/api/modules/biz/os";
import { useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import { ElMessageBox } from "element-plus";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
const { t } = useI18n();
const realEventState = ref<RealEventState>({
  subscribe: false,
  type: ""
});
const refreshLoading = ref(false);
const { paramInfo } = useConfigStore();
const { report, addConsole } = useDebugStore();
const globalReport = report.get(props.deviceId);

// 初始化查询条件
const getDefaultDateRange = (): [Date, Date] => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return [yesterday, new Date()];
};

const dateRange = ref<[Date, Date]>(getDefaultDateRange());
const isDateCheck = ref<boolean>(false);
const isButtonClick = ref(false);
const contextmenu = ref<ContextmenuInstance>();
const showTime = ref(true);
let currLine: any = undefined;
const cellContextmenu = (row): void => {
  currLine = row;
};
const searchInfo = ref("");
const searchType = ref(0);
const dialogShow = ref({
  searchProgress: false,
  percentage: 0,
  progressText: t("report.loadingText")
});
const showHiddenMark = ref<boolean>(false);
const isUpload = ref<boolean>(false);
const tableLoad = ref<boolean>(false);
const tableData = ref<ReportParam.IECRpcCommonReportRes[]>([]);
const totalNum = computed(() => {
  return tableData.value.length;
});

// ProTable列配置
const proTableColumns: ColumnProps<ReportParam.IECRpcCommonReportRes>[] = [
  { prop: "entryID", label: t("report.reportNumber"), width: 140 },
  { prop: "time", label: t("report.time"), width: 300 },
  { prop: "name", label: t("report.description") }
];

const proTable = ref();
const refreshMark = ref<boolean>(false);
const refreshName = computed(() => (refreshMark.value ? t("report.stopRefresh") : t("report.autoRefresh")));
const progressDialog = ref();
const getTableData = (): ReportParam.IECRpcCommonReportRes[] => {
  return globalReport!.commonReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
};

// 保存查询条件到缓存
const saveQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  globalReport.queryConditions.set(reportKey, {
    isDateCheck: isDateCheck.value,
    dateRange: dateRange.value && dateRange.value.length >= 2 ? [new Date(dateRange.value[0]), new Date(dateRange.value[1])] : getDefaultDateRange(),
    searchInfo: "",
    searchType: 0
  });
};

// 从缓存恢复查询条件
const restoreQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  const cached = globalReport.queryConditions.get(reportKey);
  if (cached) {
    isDateCheck.value = cached.isDateCheck;
    dateRange.value = [new Date(cached.dateRange[0]), new Date(cached.dateRange[1])];
  } else {
    // 如果没有缓存，使用默认值
    isDateCheck.value = false;
    dateRange.value = getDefaultDateRange();
  }
};

let timerId: any;
let fakeProgressTimer: any = null;
function startFakeProgress() {
  dialogShow.value.percentage = 0;
  console.log("startFakeProgress: percentage =", dialogShow.value.percentage);
  if (fakeProgressTimer) clearInterval(fakeProgressTimer);
  fakeProgressTimer = setInterval(() => {
    if (dialogShow.value.percentage < 95) {
      dialogShow.value.percentage += 5;
      console.log("progress", dialogShow.value.percentage);
    }
  }, 100);
}
function stopFakeProgress() {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer);
    fakeProgressTimer = null;
  }
  dialogShow.value.percentage = 100;
  console.log("stopFakeProgress: percentage =", dialogShow.value.percentage);
}
const timerRefresh = (): void => {
  timerId = setTimeout(() => {
    refreshList().then(() => {
      timerRefresh();
    });
  }, paramInfo.REPORT_REFRESH_TIME);
};

const refreshReport = (): void => {
  refreshMark.value = !refreshMark.value;
  if (refreshMark.value == true) {
    subRealEvent();
  } else {
    unSubRealEvent();
  }
};

const clearList = (): void => {
  globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("report.selectCompleteTimeRange"));
    return;
  }

  // 保存查询条件到缓存
  saveQueryConditions();

  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.curr极ReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[0])) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(new Date(dateRange.value[1])) : "",
    entryAfter: "",
    orderBy: "DESC"
  };
  globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  isUpload.value = false;
  tableLoad.value = true;
  isButtonClick.value = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.searchProgress = true; // 弹窗立即显示
  const reportDesc: string = globalReport!.currReportDesc;
  dialogShow.value.progressText = t("report.querying") + reportDesc;
  startFakeProgress();
  // 记录开始时间
  const start = Date.now();
  const res: ResultData<any> = await reportApi.getCommonReportListByDevice(props.deviceId, arg);
  // 计算已用时，若不足500ms则补足
  const elapsed = Date.now() - start;
  if (elapsed < 500) {
    await new Promise(resolve => setTimeout(resolve, 500 - elapsed));
  }
  stopFakeProgress();
  dialogShow.value.searchProgress = false;
  if (res.code != 1) {
    Message.warning(res.msg);
    tableLoad.value = false;
    isButtonClick.value = false;
    return;
  }
};

const refreshList = async (): Promise<void> => {
  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: "",
    stopTime: ""
  };
  const res = await reportApi.refreshTripReportByDevice(props.deviceId, arg);
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }
  if (Array.isArray(res.data)) {
    let filteredList = res.data as any;
    if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
      filteredList = res.data.filter(item => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
    }
    tableData.value = filteredList;
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);
  } else {
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    tableData.value = [];
  }
};

// 右键菜单方法
const sameSearch = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name;
  if (sameDesc && sameDesc.includes(" ")) {
    sameDesc = currLine.name.split(" ")[0];
  }
  searchType.value = 0;
  searchInfo.value = sameDesc;
};

const sameFilter = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name;
  if (sameDesc && sameDesc.includes(" ")) {
    sameDesc = currLine.name.split(" ")[0];
  }
  searchType.value = 1;
  searchInfo.value = sameDesc;
};

const dealTimeCol = (): void => {
  showTime.value = !showTime.value;
};

const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.noDataToSave") }));
    return;
  }

  // 第一步：先选择保存路径，不显示进度条
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [
      { name: "Rpt", extensions: ["rpt"] },
      { name: "Excel", extensions: ["xlsx"] }
    ]
  });

  // 如果用户取消选择路径，直接返回，不显示任何错误信息
  if (!path) {
    addConsole(t("report.exportLogCancelled"));
    return;
  }

  // 第二步：用户确认路径后，才显示进度条并开始导出
  progressDialog.value.show();
  let fakeProgress = setInterval(() => {
    if (progressDialog.value.progressDialog.percentage < 95) {
      progressDialog.value.setProgress(progressDialog.value.progressDialog.percentage + 5, t("report.exporting"));
    }
  }, 100);
  const exportList: any[] = [];
  getTableData().forEach(obj => {
    exportList.push({
      entryID: obj.entryID,
      name: obj.name,
      time: obj.time
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: exportList
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  clearInterval(fakeProgress);
  progressDialog.value.setProgress(100, t("report.exporting"));
  setTimeout(() => progressDialog.value.hide(), 500);
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm") || "确定",
    type: "success"
  }).catch(() => {
    // 用户点击关闭按钮取消操作，不需要处理
  });
};
const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const reportData = notify.data as any;

  // 跳闸报告使用同步API，不需要监听readCommonReport事件
  // 移除readCommonReport监听，避免与通用报告冲突

  if (notify.type == "fileUpload") {
    isUpload.value = true;
    dialogShow.value.searchProgress = true;
    dialogShow.value.percentage = reportData.percentage;
    dialogShow.value.progressText = t("report.fileUploading");
    return;
  }
  if (notify.type == "fileUploadSuccess") {
    dialogShow.value.searchProgress = false;
    dialogShow.value.progressText = t("report.fileUploadComplete");
    return;
  }
};

onMounted(() => {
  ipc.on("report_notify", notifyMethod);
  tableData.value = getTableData();
});

onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId);
  }
  ipc.removeAllListeners("report_notify");
});

watch(
  () => refreshMark.value,
  newValue => {
    if (newValue == true) {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = true;
      timerRefresh();
    } else {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = false;
      if (timerId) {
        clearTimeout(timerId);
      }
    }
  }
);
watch(
  () => showHiddenMark.value,
  () => {
    getTableData().length = 0;
    refreshList();
  }
);
watch(
  () => globalReport!.newname,
  () => {
    tableData.value = getTableData();
    isButtonClick.value = false;
    refreshMark.value = false;

    // 恢复查询条件而不是重置
    restoreQueryConditions();

    // refreshName现在是computed属性，会自动更新
    if (timerId) {
      clearTimeout(timerId);
    }
    unSubRealEvent();
  }
);

// 语言切换时refreshName会自动更新，不需要手动监听
// watch(
//   () => locale.value,
//   () => {
//     // refreshName现在是computed属性，会自动响应语言变化
//   }
// );

const subRealEvent = async () => {
  if (realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 订阅事件
    const res = await realEventApi.subRealEventByDevice(props.deviceId, [globalReport!.currReportType]);
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = true;
      realEventState.value.type = globalReport!.currReportType;
    }
  } finally {
    refreshLoading.value = false;
  }
};
const unSubRealEvent = async () => {
  if (!realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 取消订阅
    const res = await realEventApi.unSubRealEventByDevice(props.deviceId, { type: [realEventState.value.type] });
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = false;
      realEventState.value.type = "";
    }
  } finally {
    refreshLoading.value = false;
  }
};
onUnmounted(() => {
  unSubRealEvent();
});
</script>
<style scoped lang="scss">
.report-page {
  margin-top: 5px;
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
    .el-row {
      div {
        font-size: 14px;
        :deep(.el-date-editor) {
          width: 340px;
        }
      }
    }
  }
  .report-table {
    overflow-y: auto;
    scrollbar-width: none;
  }
}
.header {
  margin-bottom: 5px;
}
</style>
