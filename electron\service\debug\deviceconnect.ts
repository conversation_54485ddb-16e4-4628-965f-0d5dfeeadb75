"use strict";

import {
  UpadRpcConnectOptions,
  UpadRpcConnectRes,
} from "iec-upadrpc/dist/src/upadrpc/UpadRpcDef";
import { DebugDeviceInfo } from "../../interface/debug/debuginfo";

import { UPAD_RPC_DISCONNECT } from "iec-upadrpc/dist//src/UpadRpcConstants";

import { UpadRcpClient } from "iec-upadrpc/dist/src/UpadRpcClient";
import { IECResult } from "iec-common/dist/data/iecdata";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import IECCONSTANTS from "../../data/debug/iecConstants";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { IECReq } from "../../interface/debug/request";
import { IEC_EVENT, IECNotify } from "../../data/debug/notify";
import { sendMessageToUIByNotify } from "../../utils/iecUiUtils";
import { getMainWindow } from "ee-core/electron";
import { debugInfoMenuService } from "./debuginfomenu";
import { virtualDeviceMenuService } from "./virtualdevicemenu";
import { XmlFileManager } from "../../utils/xmlFileManager";
import { t } from "../../data/i18n/i18n";
import * as os from "os";

const { logger } = require("ee-core/log");

/**
 * 装置连接Service
 * <AUTHOR>
 * @class
 */
class DeviceConnectService {
  // 存储每个设备的报文监听状态
  private messageMonitorStatus: Map<string, boolean> = new Map();

  /**
   * 获取本机IP地址
   * @returns 本机IP地址
   */
  private getLocalIP(): string {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      const nets = interfaces[name];
      if (nets) {
        for (const net of nets) {
          // 跳过内部地址和IPv6地址
          if (net.family === "IPv4" && !net.internal) {
            return net.address;
          }
        }
      }
    }
    return "127.0.0.1"; // 默认返回本地回环地址
  }

  /**
   * 获取与目标IP在同一网段的本机IP地址
   * @param targetIP 目标IP地址
   * @returns 同一网段的本机IP地址
   */
  private getLocalIPInSameSubnet(targetIP: string): string {
    const interfaces = os.networkInterfaces();
    const targetParts = targetIP.split(".").map(Number);

    // 如果目标IP格式不正确，返回默认IP
    if (targetParts.length !== 4 || targetParts.some(isNaN)) {
      return this.getLocalIP();
    }

    let bestMatch = "";
    let maxMatchingBits = -1;

    for (const name of Object.keys(interfaces)) {
      const nets = interfaces[name];
      if (nets) {
        for (const net of nets) {
          // 跳过内部地址和IPv6地址
          if (net.family === "IPv4" && !net.internal) {
            const localParts = net.address.split(".").map(Number);
            const netmaskParts = net.netmask.split(".").map(Number);

            // 计算网络地址
            const localNetwork = localParts.map(
              (part, i) => part & netmaskParts[i]
            );
            const targetNetwork = targetParts.map(
              (part, i) => part & netmaskParts[i]
            );

            // 检查是否在同一网段
            const isInSameSubnet = localNetwork.every(
              (part, i) => part === targetNetwork[i]
            );

            if (isInSameSubnet) {
              // 计算匹配的位数（子网掩码的位数）
              const matchingBits = netmaskParts.reduce((bits, part) => {
                // 计算每个字节中1的个数
                let count = 0;
                let temp = part;
                while (temp) {
                  count += temp & 1;
                  temp >>= 1;
                }
                return bits + count;
              }, 0);

              // 选择匹配位数最多的（最精确的子网）
              if (matchingBits > maxMatchingBits) {
                maxMatchingBits = matchingBits;
                bestMatch = net.address;
              }
            }
          }
        }
      }
    }

    // 如果找到同网段的IP，返回它；否则返回默认IP
    const result = bestMatch || this.getLocalIP();
    logger.debug(
      `为装置IP ${targetIP} 选择的本机IP: ${result} (匹配位数: ${maxMatchingBits})`
    );
    return result;
  }

  /**
   * 测试同网段IP选择功能（仅用于调试）
   * @param targetIP 目标IP地址
   */
  public testSubnetSelection(targetIP: string): void {
    logger.info(`测试同网段IP选择 - 目标IP: ${targetIP}`);
    const interfaces = os.networkInterfaces();

    // 显示所有网络接口信息
    for (const name of Object.keys(interfaces)) {
      const nets = interfaces[name];
      if (nets) {
        for (const net of nets) {
          if (net.family === "IPv4" && !net.internal) {
            logger.info(
              `网络接口 ${name}: IP=${net.address}, 子网掩码=${net.netmask}`
            );
          }
        }
      }
    }

    const selectedIP = this.getLocalIPInSameSubnet(targetIP);
    logger.info(`为目标IP ${targetIP} 选择的本机IP: ${selectedIP}`);
  }

  // 检查装置是否连接，连接返回true，未连接返回false
  async isConnected(id: string): Promise<boolean> {
    logger.info(
      `[DeviceConnectService] ${t("services.deviceConnect.getConnection")} 入参:`,
      id
    );
    try {
      const client = GlobalDeviceData.getInstance().getClient(id);
      if (client) {
        logger.debug(
          `[DeviceConnectService] isConnected - ${t("services.deviceConnect.clientObtained")}: ${id}`
        );
        return client.isConnected();
      }
      logger.warn(
        `[DeviceConnectService] isConnected - ${t("services.deviceConnect.clientNotObtained")}: ${id}`
      );
      return false;
    } catch (error) {
      logger.error(
        `[DeviceConnectService] isConnected ${t("services.deviceConnect.connectionException")}:`,
        error
      );
      return false;
    }
  }

  // 检查装置是否连接
  async checkConnection(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[DeviceConnectService] ${t("services.deviceConnect.getConnection")} 入参:`,
      JSON.stringify(req)
    );
    try {
      const connected = await deviceConnectService.isConnected(req.head.id);
      if (!connected) {
        logger.warn(
          `[DeviceConnectService] ${t("services.deviceConnect.deviceNotConnected")}`
        );
      } else {
        logger.debug(
          `[DeviceConnectService] checkConnection - ${t("services.deviceConnect.deviceConnected")}: ${req.head.id}`
        );
      }
      return connected;
    } catch (error) {
      logger.error(
        `[DeviceConnectService] checkConnection ${t("services.deviceConnect.connectionCheckException")}:`,
        error
      );
      return false;
    }
  }

  /**
   * 连接装置 */
  async connectDeviceByRpc(
    dataStr: string
  ): Promise<IECResult<UpadRpcConnectRes>> {
    logger.info(
      `[DeviceConnectService] ${t("services.deviceConnect.connectDeviceByRpc")} 入参:`,
      dataStr
    );
    const globalDeviceData = GlobalDeviceData.getInstance();
    const data: DebugDeviceInfo = JSON.parse(dataStr);
    // 判断是否存在错误码信息,首次连接时如果连接出错，提供默认错误码，但是
    let singleGlobalDeviceInfo = globalDeviceData.getDeviceInfoGlobal(data.id);
    if (!singleGlobalDeviceInfo) {
      singleGlobalDeviceInfo = new SingleGlobalDeviceInfo(data.id);
      await singleGlobalDeviceInfo.initEnumTypeAndFile();
    }
    const enumType = singleGlobalDeviceInfo.enumTypes.get("ServiceError");
    // 调取接口
    const connectOptios: UpadRpcConnectOptions = {
      ip: data.ip,
      port: Number(data.port),
      connectTimeout: Number(data.connectTimeout),
      readTimeout: Number(data.readTimeout),
      libPath: IECCONSTANTS.PATH_DLL + "\\upadrpc",
    };
    logger.info(`${t("services.deviceConnect.callInterface")}:`, connectOptios);
    // 使用装置的id作为key
    const key = data.id;
    // 从全局变量中获取连接对象
    let upadRcpClient = globalDeviceData.deviceInfoMap.get(key)?.deviceClient;
    if (upadRcpClient && upadRcpClient.isConnected()) {
      await upadRcpClient.disconnect();
    }
    upadRcpClient = new UpadRcpClient();

    // 监听报文消息
    upadRcpClient.on("upadRpcMessage", (data) => {
      // 只有在启用报文监视时才发送数据到前端
      if (this.messageMonitorStatus.get(key)) {
        // console.log("收到报文数据:", data);

        // 获取本机IP和装置IP
        const deviceIP = connectOptios.ip;
        const localIP = this.getLocalIPInSameSubnet(deviceIP);

        // 调试日志
        logger.debug(`报文监视 - 装置IP: ${deviceIP}, 本机IP: ${localIP}`);

        // 根据报文类型确定方向显示
        let directionDisplay = "";
        if (data && typeof data === "object" && data.type) {
          if (data.type === "send") {
            directionDisplay = `${localIP} → ${deviceIP}`;
          } else if (data.type === "receive") {
            directionDisplay = `${deviceIP} → ${localIP}`;
          } else {
            directionDisplay = `${localIP} ↔ ${deviceIP}`;
          }
        } else {
          directionDisplay = `${localIP} ↔ ${deviceIP}`;
        }

        // 发送报文数据到前端界面
        const notify: IECNotify = {
          type: "upadRpcMessage",
          data: {
            timestamp: new Date().toISOString(),
            message: data,
            localIP: localIP,
            deviceIP: deviceIP,
            direction: directionDisplay,
          },
          deviceId: key,
        };
        sendMessageToUIByNotify(IEC_EVENT.NOTIFY, notify, getMainWindow());
      }
    });

    upadRcpClient.on(UPAD_RPC_DISCONNECT, (data) => {
      // 通知前端修改连接状态
      const notify: IECNotify = {
        type: "disconnect",
        data: null,
        deviceId: key, // 添加设备ID，确保前端能识别是哪个设备断开
      };
      sendMessageToUIByNotify(IEC_EVENT.NOTIFY, notify, getMainWindow());

      // 清理该设备的报文监视状态
      this.messageMonitorStatus.delete(key);

      logger.info(
        `[DeviceConnectService] 设备断开连接通知已发送，设备ID: ${key}`
      );
    });

    let connResult;
    for (let i = 0; i < 3; i++) {
      connResult = await upadRcpClient.connect(connectOptios);
      if (connResult.isSuccess()) {
        break;
      } else {
        logger.warn(
          `${t("services.deviceConnect.connectionAttempt")} ${i + 1}:`,
          connResult
        );
        // 可选：每次重试间隔一段时间
        if (i < 2) {
          await new Promise((res) => setTimeout(res, 500));
        }
      }
    }
    if (!connResult.isSuccess()) {
      let msg = enumType?.get(connResult.data?.code + "");
      if (!msg) {
        msg = t("errors.connectionFailed");
      }
      if (connResult.code === 10) {
        msg = t("errors.connectionTimeout");
      }
      connResult.msg = msg;
      return connResult;
    }
    logger.info(t("common.connectionSuccess"), connResult);
    // 将连接成功的连接对象缓存
    let realSingleGlobalDeviceInfo = globalDeviceData.getDeviceInfoGlobal(
      data.id
    );
    if (!realSingleGlobalDeviceInfo) {
      realSingleGlobalDeviceInfo = new SingleGlobalDeviceInfo(data.id);
    }
    // 复位数据
    realSingleGlobalDeviceInfo.resetData();
    realSingleGlobalDeviceInfo.deviceClient = upadRcpClient;

    // 根据装置类型加载菜单：虚拟化装置使用固定菜单，普通装置读取XML文件
    if (virtualDeviceMenuService.isVirtualDevice(data)) {
      logger.info(
        `[DeviceConnectService] 检测到虚拟化装置，使用固定菜单: ${data.id}`
      );
      // 虚拟化装置直接使用固定菜单，无需读取XML文件
      const virtualMenuData =
        virtualDeviceMenuService.getVirtualDeviceMenu(key);
      realSingleGlobalDeviceInfo.debugInfo = virtualMenuData;

      logger.info(
        `[DeviceConnectService] 虚拟化装置菜单初始化完成，菜单数量: ${virtualMenuData.menus?.length || 0}`
      );
      logger.debug(
        `[DeviceConnectService] 虚拟化装置菜单详情:`,
        virtualMenuData.menus?.map((menu) => ({
          name: menu.name,
          desc: menu.desc,
          fc: menu.fc,
          itemsCount: menu.items?.length || 0,
          menusCount: menu.menus?.length || 0,
        }))
      );

      // 设置为已缓存状态，避免重复读取
      realSingleGlobalDeviceInfo.compareMd5Result = true;

      globalDeviceData.setDeviceInfoGlobal(key, realSingleGlobalDeviceInfo);
    } else {
      logger.info(
        `[DeviceConnectService] 普通装置，读取debug_info.xml文件: ${data.id}`
      );

      // 清理可能的缓存数据，确保重新读取XML文件
      realSingleGlobalDeviceInfo.debugInfo = undefined;
      realSingleGlobalDeviceInfo.deviceInfoMenus = [];
      realSingleGlobalDeviceInfo.compareMd5Result = false;

      // 比较md5码，然后设置文件位置
      const xmlFileManager = new XmlFileManager();
      await xmlFileManager.setDebugInfoFilePath(
        key,
        upadRcpClient,
        realSingleGlobalDeviceInfo
      );
      // 往realSingleGlobalDeviceInfo添加debugInfo 和debugItemMap
      globalDeviceData.setDeviceInfoGlobal(key, realSingleGlobalDeviceInfo);
      realSingleGlobalDeviceInfo.debugInfo =
        await debugInfoMenuService.getDebugInfo(key);

      logger.info(
        `[DeviceConnectService] 普通装置XML读取完成，菜单数量: ${realSingleGlobalDeviceInfo.debugInfo?.menus?.length || 0}`
      );
    }
    logger.debug(
      `[DeviceConnectService] connectDeviceByRpc - ${t("services.deviceConnect.connectionSuccessful")}: ${data.id}`
    );
    return connResult;
  }

  // 断开连接
  async disconnectDevice(deviceId: string): Promise<any> {
    logger.info(
      `[DeviceConnectService] ${t("services.deviceConnect.disconnectDevice")} 入参:`,
      deviceId
    );
    try {
      // 获取连接
      const deviceInfoGlobal =
        GlobalDeviceData.getInstance().getDeviceInfoGlobal(deviceId);
      let upadRcpClient = deviceInfoGlobal?.deviceClient;
      if (upadRcpClient && typeof UpadRcpClient) {
        const result = await upadRcpClient.disconnect();
        if (result) {
          // 删除连接对象
          upadRcpClient = undefined;
        }
        logger.info(
          `[DeviceConnectService] disconnectDevice - ${t("services.deviceConnect.disconnectSuccessful")}: ${deviceId}`
        );
        return result;
      }
    } catch (error) {
      logger.error(
        `[DeviceConnectService] disconnectDevice ${t("services.deviceConnect.disconnectException")}:`,
        error
      );
      throw error;
    }
  }

  // 启动报文监视
  async startMessageMonitor(deviceId: string): Promise<boolean> {
    logger.info(
      `[DeviceConnectService] ${t("services.deviceConnect.startMessageMonitor")} 入参:`,
      deviceId
    );
    try {
      // 检查设备是否连接
      const deviceInfoGlobal =
        GlobalDeviceData.getInstance().getDeviceInfoGlobal(deviceId);
      const upadRcpClient = deviceInfoGlobal?.deviceClient;

      if (upadRcpClient && upadRcpClient.isConnected()) {
        this.messageMonitorStatus.set(deviceId, true);
        logger.info(
          `[DeviceConnectService] startMessageMonitor - ${t("services.deviceConnect.messageMonitorStarted")}: ${deviceId}`
        );
        return true;
      } else {
        logger.warn(
          `[DeviceConnectService] startMessageMonitor - ${t("services.deviceConnect.deviceNotConnected")}: ${deviceId}`
        );
        return false;
      }
    } catch (error) {
      logger.error(
        `[DeviceConnectService] startMessageMonitor ${t("services.deviceConnect.messageMonitorException")}:`,
        error
      );
      return false;
    }
  }

  // 停止报文监视
  async stopMessageMonitor(deviceId: string): Promise<boolean> {
    logger.info(
      `[DeviceConnectService] ${t("services.deviceConnect.stopMessageMonitor")} 入参:`,
      deviceId
    );
    try {
      this.messageMonitorStatus.set(deviceId, false);
      logger.info(
        `[DeviceConnectService] stopMessageMonitor - ${t("services.deviceConnect.messageMonitorStopped")}: ${deviceId}`
      );
      return true;
    } catch (error) {
      logger.error(
        `[DeviceConnectService] stopMessageMonitor ${t("services.deviceConnect.messageMonitorException")}:`,
        error
      );
      return false;
    }
  }

  // 获取报文监视状态
  getMessageMonitorStatus(deviceId: string): boolean {
    return this.messageMonitorStatus.get(deviceId) || false;
  }
}

DeviceConnectService.toString = () => "[class DeviceConnectService]";
const deviceConnectService = new DeviceConnectService();

export { DeviceConnectService, deviceConnectService };
