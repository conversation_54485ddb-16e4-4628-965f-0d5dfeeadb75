<template>
  <div class="item-main" @click="handleClick">
    <el-tooltip :content="t('layout.language.title')" :placement="getTooltipPlacement()" effect="dark" :show-after="500">
      <el-dropdown trigger="click" @visible-change="handleVisibleChange" placement="right-start" :popper-options="{ strategy: 'fixed' }">
        <div class="language-trigger">
          <i class="iconfont toolBar-icon" style="display: flex; flex-direction: column">
            <svg-icon icon="eva:globe-2-fill"></svg-icon>
            <span v-if="globalStore.checkColumnLayout()" style="margin-top: 6px; font-size: 12px">{{ t("layout.language.title") }}</span>
          </i>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="language-dropdown">
            <el-dropdown-item @click="handleCommand('zh')" class="language-item">
              <span class="language-text">
                <svg-icon icon="local:flag-china" style="font-size: 16px" />
                {{ t("layout.language.zh") }}
              </span>
            </el-dropdown-item>
            <el-dropdown-item @click="handleCommand('en')" class="language-item">
              <span class="language-text">
                <svg-icon icon="local:flag-united-kingdom" style="font-size: 16px" />
                {{ t("layout.language.en") }}
              </span>
            </el-dropdown-item>
            <el-dropdown-item @click="handleCommand('ru')" class="language-item">
              <span class="language-text">
                <svg-icon icon="local:flag-russia" style="font-size: 16px" />
                {{ t("layout.language.ru") }}
              </span>
            </el-dropdown-item>
            <el-dropdown-item @click="handleCommand('es')" class="language-item">
              <span class="language-text">
                <svg-icon icon="local:flag-spain" style="font-size: 16px" />
                {{ t("layout.language.es") }}
              </span>
            </el-dropdown-item>
            <el-dropdown-item @click="handleCommand('fr')" class="language-item">
              <span class="language-text">
                <svg-icon icon="local:flag-france" style="font-size: 16px" />
                {{ t("layout.language.fr") }}
              </span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import { LanguageType } from "@/stores/interface";
import { languageApi } from "@/api/modules/sys/language";
import { ElMessage } from "element-plus";

const globalStore = useGlobalStore();
const { locale, t } = useI18n();

const handleCommand = async (command: string) => {
  console.log("Language command:", command);

  try {
    // 设置前端语言
    locale.value = command;
    localStorage.setItem("language", command);
    globalStore.setGlobalState("language", command as LanguageType);

    // 同步到后端
    await languageApi.syncLanguage({ language: command });
    console.log("Language synced to backend successfully:", command);
  } catch (error) {
    console.error("Failed to sync language to backend:", error);
    // 如果同步失败，显示警告但不影响前端语言切换
    ElMessage.warning(t("common.languageSyncWarning") || "Language sync to backend failed, but frontend language changed successfully");
  }
};

const handleClick = () => {
  console.log("Language button clicked");
};

const handleVisibleChange = (visible: boolean) => {
  console.log("Dropdown visible changed:", visible);
};

// 根据布局类型动态获取tooltip位置
const getTooltipPlacement = (): toolTipTypes => {
  const layout = globalStore.layout;
  // 横向布局时tooltip显示在底部，其他布局显示在右侧
  return layout === "transverse" ? "bottom" : "right";
};
</script>

<style scoped lang="scss">
.item-main {
  position: relative;
  z-index: 3001;
  display: flex;
  flex-flow: column wrap;
  place-content: space-around center;
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
}
.language-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  transition: all 0.3s ease;
  &:hover {
    transform: scale(1.05);
  }
  .toolBar-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    .svg-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
:deep(.language-dropdown) {
  min-width: 120px;
  padding: 5px 0;
  margin-left: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  .language-item {
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.3s ease;
    &:hover {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
    .language-text {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 6px;
      line-height: 1.5;

      .svg-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        width: 16px;
        height: 16px;
      }
    }
  }
}
:deep(.el-popper) {
  z-index: 3001 !important;
}
:deep(.el-dropdown-menu__item) {
  &:hover {
    background-color: var(--el-color-primary-light-9);
  }
  &.is-active {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
}
</style>
