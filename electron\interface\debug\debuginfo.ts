interface DebugInfo {
  configVersion: string;
  header: Header;
  menus: DebugInfoMenu[];
  dataTypeTemplates: DataTypeTemplates;
}

interface DataTypeTemplates  {
  EnumType : EnumType[];
}

interface EnumType {
  id : string;
  valMap: Map<String, String>;
}
interface Header {
  updateTime?: string;
  md5?: string;
}
interface DebugInfoMenu {
  name: string;
  desc: string;
  fc?: string;
  method?: string;
  items?: DebugInfoItem[];
  menus?: DebugInfoMenu[];
}

interface GroupInfoItem {
  id: number;
  name: string;
  desc: string;
  fc?: string;
  count?: number;
}

interface DebugInfoItem {
  index?: number;
  name: string;
  vKey?: string;
  bType?: string;
  format?: string;
  qKey?: string;
  unit?: string;
  desc: string;
  cn?: string;
  grp?: string;
  inf?: string;
  p_min?: string;
  p_max?: string;
  p_norm?:string;
  s_norm?:string;
  s_min?: string;
  s_max?: string;
  step?: string;
  type?: string;
  value?:unknown;
  quality?: unknown;
  fc?: string;
  grpName?: string;
}

interface DebugDeviceInfo {
  id: string;
  ip: string;
  name: string;
  port: string;
  encrypted: boolean;
  isVirtual?: boolean; // 虚拟化装置
  prjType: number;
  deviceType: number;
  isConnect: boolean;
  isActive: boolean;
  connectTimeout?: number;
  readTimeout?: number;
  paramTimeout?: number;
  connectTime: string;
}

interface MenuIdName {
  id: string;
  type: string;
  names: Array<string>;
}

export type { DebugInfo, DebugInfoMenu, DebugInfoItem , Header , DataTypeTemplates , EnumType , DebugDeviceInfo, MenuIdName, GroupInfoItem};
