import { DebugInfo, DebugInfoMenu, DebugInfoItem, Header } from "../../interface/debug/debuginfo";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";

/**
 * 虚拟化装置菜单服务
 * 为虚拟化装置提供固定的菜单结构，无需读取debug_info.xml文件
 * <AUTHOR>
 * @class
 */
class VirtualDeviceMenuService {
  constructor() {
    logger.info("[VirtualDeviceMenuService] 虚拟化装置菜单服务初始化完成");
  }

  /**
   * 生成虚拟化装置的固定菜单结构
   * @param deviceId 装置ID
   * @returns DebugInfo 固定的菜单结构
   */
  public getVirtualDeviceMenu(deviceId: string): DebugInfo {
    logger.info(`[VirtualDeviceMenuService] 生成虚拟化装置菜单: ${deviceId}`);
    
    // 虚拟化装置直接使用扁平化的菜单结构，无需"虚拟化功能"父级菜单
    const virtualMenu: DebugInfo = {
      configVersion: "1.0.0",
      header: {
        updateTime: new Date().toISOString(),
        md5: "virtual_device_menu",
      },
      dataTypeTemplates: {
        EnumType: [],
      },
      menus: [
        {
          name: t("virtualDevice.menus.analog.name"),
          desc: t("virtualDevice.menus.analog.desc"),
          fc: "read_analoy_para",
          method: "read_analoy_para",
          items: this.generateVirtualItems("analoy"),
          menus: [],
        },
        {
          name: t("virtualDevice.menus.digitalInput.name"),
          desc: t("virtualDevice.menus.digitalInput.desc"),
          fc: "read_bi_para",
          method: "read_bi_para",
          items: this.generateVirtualItems("bi"),
          menus: [],
        },
        {
          name: t("virtualDevice.menus.digitalOutput.name"),
          desc: t("virtualDevice.menus.digitalOutput.desc"),
          fc: "read_bo_para",
          method: "read_bo_para",
          items: this.generateVirtualItems("bo"),
          menus: [],
        },
        {
          name: t("virtualDevice.menus.fault.name"),
          desc: t("virtualDevice.menus.fault.desc"),
          fc: "read_fault_para",
          method: "read_fault_para",
          items: this.generateVirtualItems("fault"),
          menus: [],
        },
        {
          name: t("virtualDevice.menus.led.name"),
          desc: t("virtualDevice.menus.led.desc"),
          fc: "read_led_para",
          method: "read_led_para",
          items: this.generateVirtualItems("led"),
          menus: [],
        },
        {
          name: t("virtualDevice.menus.waveReplay.name"),
          desc: t("virtualDevice.menus.waveReplay.desc"),
          fc: "wave_replay",
          method: "wave_replay",
          items: this.generateVirtualItems("wave"),
          menus: [],
        },
      ],
    };

    logger.info(
      `[VirtualDeviceMenuService] 虚拟化装置菜单生成完成: ${deviceId}`
    );
    logger.debug(
      `[VirtualDeviceMenuService] 虚拟化装置菜单详情:`,
      virtualMenu.menus.map((menu) => ({
        name: menu.name,
        desc: menu.desc,
        fc: menu.fc,
        itemsCount: menu.items?.length || 0,
        menusCount: menu.menus?.length || 0,
        items:
          menu.items?.map((item) => ({ name: item.name, desc: item.desc })) ||
          [],
      }))
    );
    return virtualMenu;
  }

  /**
   * 生成虚拟的菜单项
   * @param type 类型
   * @returns DebugInfoItem[] 虚拟菜单项数组
   */
  private generateVirtualItems(type: string): DebugInfoItem[] {
    const baseItems: Record<string, DebugInfoItem[]> = {
      analoy: [
        {
          name: "AI_1",
          vKey: "ai_1",
          bType: "AI",
          format: "%.2f",
          qKey: "ai_1_q",
          unit: "V",
          desc: t("virtualDevice.items.analog.desc"),
          cn: t("virtualDevice.items.analog.name"),
          grp: "analoy",
          inf: "input",
          p_norm: "0",
          s_norm: "0",
          p_min: "0",
          p_max: "100",
          s_min: "0",
          s_max: "100",
          step: "0.1",
          type: "REAL32"
        }
      ],
      bi: [
        {
          name: "BI_1",
          vKey: "bi_1",
          bType: "BI",
          format: "%d",
          qKey: "bi_1_q",
          unit: "",
          desc: t("virtualDevice.items.digitalInput.desc"),
          cn: t("virtualDevice.items.digitalInput.name"),
          grp: "bi",
          inf: "input",
          p_norm: "0",
          s_norm: "0",
          p_min: "0",
          p_max: "1",
          s_min: "0",
          s_max: "1",
          step: "1",
          type: "BOOL"
        }
      ],
      bo: [
        {
          name: "BO_1",
          vKey: "bo_1",
          bType: "BO",
          format: "%d",
          qKey: "bo_1_q",
          unit: "",
          desc: t("virtualDevice.items.digitalOutput.desc"),
          cn: t("virtualDevice.items.digitalOutput.name"),
          grp: "bo",
          inf: "output",
          p_norm: "0",
          s_norm: "0",
          p_min: "0",
          p_max: "1",
          s_min: "0",
          s_max: "1",
          step: "1",
          type: "BOOL"
        }
      ],
      fault: [
        {
          name: "FAULT_1",
          vKey: "fault_1",
          bType: "FAULT",
          format: "%d",
          qKey: "fault_1_q",
          unit: "",
          desc: t("virtualDevice.items.fault.desc"),
          cn: t("virtualDevice.items.fault.name"),
          grp: "fault",
          inf: "status",
          p_norm: "0",
          s_norm: "0",
          p_min: "0",
          p_max: "1",
          s_min: "0",
          s_max: "1",
          step: "1",
          type: "BOOL"
        }
      ],
      led: [
        {
          name: "LED_BRIGHTNESS",
          vKey: "led_brightness",
          bType: "LED",
          format: "%d",
          qKey: "led_brightness_q",
          unit: "%",
          desc: t("virtualDevice.items.led.desc"),
          cn: t("virtualDevice.items.led.name"),
          grp: "led",
          inf: "config",
          p_norm: "50",
          s_norm: "50",
          p_min: "0",
          p_max: "100",
          s_min: "0",
          s_max: "100",
          step: "1",
          type: "INT32"
        }
      ],
      wave: [
        {
          name: "WAVE_FILE",
          vKey: "wave_file",
          bType: "WAVE",
          format: "%s",
          qKey: "wave_file_q",
          unit: "",
          desc: t("virtualDevice.items.waveReplay.desc"),
          cn: t("virtualDevice.items.waveReplay.name"),
          grp: "wave",
          inf: "file",
          p_norm: "",
          s_norm: "",
          p_min: "",
          p_max: "",
          s_min: "",
          s_max: "",
          step: "",
          type: "STRING"
        }
      ]
    };

    return baseItems[type] || [];
  }

  /**
   * 检查装置是否为虚拟化装置
   * @param deviceInfo 装置信息
   * @returns boolean 是否为虚拟化装置
   */
  public isVirtualDevice(deviceInfo: any): boolean {
    return deviceInfo?.isVirtual === true;
  }
}

const virtualDeviceMenuService = new VirtualDeviceMenuService();
export { VirtualDeviceMenuService, virtualDeviceMenuService };