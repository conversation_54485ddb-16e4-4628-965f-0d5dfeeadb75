import { moduleIpcRequest } from "@/api/request";

// 虚拟化装置相关接口类型定义
export interface VirtualAnalogParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  amplitude: number; // 幅值
  phase: number; // 相角
  isModified: boolean;
  originalAmplitude: number;
  originalPhase: number;
}

export interface VirtualDigitalInputParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  amplitude: number; // 幅值
  originalAmplitude: number;
  isModified: boolean;
}

export interface VirtualDigitalOutputParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  amplitude: number; // 幅值
}

export interface VirtualFaultParam {
  originalAmplitude: number;
  isModified: boolean;
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  amplitude: number; // 幅值
}

export interface VirtualLedParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  value: any; // 值
}

// 创建IPC请求实例
const ipc = moduleIpcRequest("controller/debug/virtualdevice/");

// 虚拟化装置API服务
export const virtualDeviceApi = {
  /**
   * 通用查询接口 - 支持多种参数类型查询
   * cmdType: read_analoy_para（模拟量）、read_bi_para（开入量）、read_bo_para（开出量）、read_fault_para（故障量）、read_led_para（led参数）
   */
  getVirtualParamsByDevice(deviceId: string, cmdType: string, params?: any) {
    return ipc.iecInvokeWithDevice<{ list: any[]; total: number }>("getVirtualParams", { cmdType, ...params }, deviceId);
  },

  /**
   * 通用修改接口 - 支持多种参数类型修改
   * cmdType: analoy_input（模拟量）、bi_input（开入量）、fault_input（故障量）
   */
  updateVirtualParamsByDevice(deviceId: string, cmdType: string, params: any[]) {
    return ipc.iecInvokeWithDevice<any>("updateVirtualParams", { cmdType, params }, deviceId);
  }
};
