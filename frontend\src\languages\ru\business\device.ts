export default {
  virtualSummary: {
    description:
      "Функциональный интерфейс, разработанный специально для виртуальных устройств, обеспечивающий чистый и эффективный пользовательский опыт",
    analogTitle: "Управление аналоговыми параметрами",
    analogDesc: "Настройка, мониторинг и управление аналоговыми параметрами",
    digitalInputTitle: "Мониторинг цифровых входов",
    digitalInputDesc: "Мониторинг в реальном времени и отображение состояния цифровых входов",
    digitalOutputTitle: "Управление цифровыми выходами",
    digitalOutputDesc: "Операции управления и контроль состояния для цифровых выходов",
    faultTitle: "Сигналы неисправностей",
    faultDesc: "Мониторинг и диагностический анализ сигналов неисправностей",
    ledTitle: "Параметры светодиодов",
    ledDesc: "Настройка и управление параметрами светодиодных индикаторов",
    waveTitle: "Воспроизведение неисправностей",
    waveDesc: "Воспроизведение и анализ файлов волн неисправностей",
    analogParam: {
      title: "Аналоговые параметры",
      description: "Настройка и управление аналоговыми параметрами"
    },
    digitalInput: {
      title: "Цифровые входы",
      description: "Мониторинг в реальном времени цифровых входов"
    },
    digitalOutput: {
      title: "Цифровые выходы",
      description: "Управление и контроль цифровых выходов"
    },
    faultParam: {
      title: "Параметры неисправностей",
      description: "Настройка и диагностика неисправностей"
    },
    ledParam: {
      title: "Параметры светодиодов",
      description: "Настройка светодиодных индикаторов"
    },
    waveReplay: {
      title: "Воспроизведение волн",
      description: "Анализ файлов волн"
    },
    advantages: "Ключевые преимущества",
    advantage1: "Не требуется физическое устройство, что снижает затраты и риски",
    advantage2: "Упрощенный функциональный интерфейс, повышающий операционную эффективность",
    advantage3: "Быстрое подключение без ожидания загрузки XML-файлов",
    advantage4: "Унифицированное функциональное меню, обеспечивающее согласованный пользовательский опыт",
    usage: "Инструкции по использованию",
    usageDescription:
      "Нажмите на функциональное меню слева, чтобы получить прямой доступ к соответствующему интерфейсу виртуального устройства. Все функции были оптимизированы на основе исходной структуры, чтобы обеспечить более профессиональный и эффективный пользовательский опыт."
  },

  configure: {
    remoteSet: "Дистанционная настройка"
  },
  console: {
    title: "Консоль",
    clear: "Очистить",
    selectAll: "Выбрать все",
    copy: "Копировать",
    copySuccess: "Копирование успешно",
    noTextSelected: "Нет выбранного текста",
    copyFailed: "Копирование не удалось",
    clearSuccess: "Консоль очищена",
    collapse: "Свернуть",
    expand: "Развернуть"
  },
  groupInfo: {
    title: "Информация о группе",
    virtualTitle: "Функции виртуального устройства",
    table: {
      id: "Номер",
      name: "Название",
      desc: "Описание",
      fc: "FC",
      count: "Количество",
      method: "Метод",
      keyword: "Ключевое слово",
      inherit: "Наследовать"
    },
    messages: {
      fetchDataError: "Произошла ошибка при получении данных",
      fetchedData: "Полученные данные:",
      fetchCustomGroupFailed: "Не удалось получить информацию о пользовательской группе",
      deviceConnectError: "Ошибка подключения устройства",
      parameterSaveSuccess: "Параметры успешно сохранены",
      commandExecuteFailed: "Не удалось выполнить команду"
    },
    buttons: {
      connect: "Подключить",
      disconnect: "Отключить",
      save: "Сохранить",
      refresh: "Обновить"
    },
    status: {
      connected: "Подключено",
      disconnected: "Отключено",
      connecting: "Подключение...",
      operationFailed: "Операция не удалась",
      connectionTimeout: "Таймаут подключения"
    },
    tooltips: {
      refreshData: "Обновить данные",
      editSettings: "Редактировать настройки"
    },
    notifications: {
      saveSuccess: "Сохранение успешно",
      saveFailed: "Ошибка сохранения"
    }
  },
  treeClickLog: "Клик по дереву treeClick : ",
  contentView: "Просмотр содержимого",
  emptyDeviceId: "Текущий ID устройства пуст",
  invalidResponseStructure: "Недействительная структура ответа",
  formattedMenuDataLog: "Отформатированные данные меню ===",
  allSettings: "Все установленные значения",
  allEditSpSettings: "Все установленные значения одной зоны",
  allEditSgSettings: "Все установленные значения нескольких зон",
  deviceTreeDataLog: "Данные дерева устройств",
  failedToLoadMenu: "Не удалось загрузить меню устройства:",
  innerTabs: {
    contentView: "Контент",
    fileUpload: "Загрузка",
    fileDownload: "Скачать",
    deviceTime: "Синхр.",
    deviceOperate: "Устройство",
    variableDebug: "Отладка",
    oneClickBackup: "Резерв",
    entryConfig: "Записи",
    tabClickLog: "Вкладка:"
  },
  devices: {
    notConnectedAlt: "Устройство не подключено",
    pleaseConnect: "Пожалуйста, сначала подключите устройство!"
  },
  list: {
    unnamedDevice: "Безымянное устройство",
    connected: "Подключено",
    disconnected: "Отключено",
    connect: "Подключить",
    edit: "Редактировать",
    disconnect: "Отключить",
    remove: "Удалить",
    noDeviceFound: "Устройство не найдено",
    handleClickLog: "Клик handleListClick:",
    disconnectBeforeEdit: "Пожалуйста, сначала отключите соединение перед редактированием",
    connectSuccess: "Устройство {name}: подключение успешно",
    connectExist: "Устройство {name}: соединение уже существует",
    connectFailed: "Устройство {name}: подключение не удалось",
    connectFailedReason: "Причина неудачи подключения устройства:",
    disconnectedSuccess: "Устройство {name}: отключено",
    disconnectedNotify: "Устройство {name} соединение разорвано",
    currentDisconnectedNotify: "Текущее устройство соединение разорвано",
    operateFailed: "Устройство {name}: операция не удалась",
    disconnectBeforeDelete: "Пожалуйста, сначала отключите соединение перед удалением",
    dataLog: "Данные:",
    ipPortExist: "Этот IP и порт уже существуют, пожалуйста, не добавляйте повторно",
    messageMonitor: "Мониторинг сообщений",
    connectFirst: "Пожалуйста, сначала подключите устройство",
    messageMonitorOpened: "Устройство {name}: мониторинг сообщений открыт",
    deviceAlreadyConnected: "Устройство уже подключено, пожалуйста, сначала отключите его перед подключением"
  },
  messageMonitor: {
    title: "Мониторинг сообщений",
    start: "Начать мониторинг",
    stop: "Остановить мониторинг",
    clear: "Очистить",
    export: "Экспорт",
    expand: "Развернуть",
    collapse: "Свернуть",
    close: "Закрыть",
    messageType: "Сообщение",
    noMessages: "Нет данных сообщений",
    noMessagesToExport: "Нет данных сообщений для экспорта",
    startSuccess: "Начат мониторинг сообщений",
    stopSuccess: "Остановлен мониторинг сообщений",
    stopSuccessWithDevice: "Устройство {name}: Остановлен мониторинг сообщений",
    clearSuccess: "Очистка сообщений успешна",
    exportSuccess: "Экспорт сообщений успешен",
    exportFailed: "Экспорт сообщений не удался",
    toggleFailed: "Переключение состояния мониторинга не удалось",
    pauseScroll: "Приостановить прокрутку",
    resumeScroll: "Возобновить прокрутку",
    monitoring: "Мониторинг",
    copy: "Копировать",
    copySuccess: "Сообщение скопировано в буфер обмена",
    copyFailed: "Копирование не удалось",
    autoScrollEnabled: "Автопрокрутка включена",
    autoScrollDisabled: "Автопрокрутка приостановлена",
    send: "Отправить",
    receive: "Получить",
    message: "Сообщение"
  },
  search: {
    placeholder: "Поиск устройства",
    ipPortExist: "Этот IP и порт уже существуют, пожалуйста, не добавляйте повторно"
  },
  summaryPie: {
    other: "Другое",
    title: "Соотношение значений",
    subtext: "Распределение значений"
  },
  deviceInfo: {
    title: "Информация об устройстве",
    export: "Экспорт",
    exportTitle: "Экспорт информации об устройстве",
    exportLoading: "Экспорт основной информации об устройстве...",
    exportSuccess: "Экспорт основной информации об устройстве успешен",
    exportFailed: "Экспорт основной информации об устройстве не удался",
    getInfoFailed: "Получение информации об устройстве не удалось. Причина неудачи: {msg}",
    getInfoFailedEmpty: "Получение информации об устройстве не удалось. Причина неудачи: данные пусты!",
    defaultFileName: "Информация об устройстве.xlsx",
    confirm: "Подтвердить",
    tip: "Подсказка"
  },
  allParamSetting: {
    title: "Все установленные значения",
    autoRefresh: "Автообновление",
    refresh: "Обновить",
    confirm: "Подтвердить",
    import: "Импорт",
    export: "Экспорт",
    groupTitle: "Группа установленных значений:",
    allGroups: "Все",
    noDataToImport: "Нет данных для импорта",
    importSuccess: "Импорт установленных значений успешен",
    importFailed: "Импорт установленных значений не удался: {msg}",
    requestFailed: "Запрос не удался, пожалуйста, попробуйте позже",
    queryFailed: "Запрос установленных значений не удался: {msg}",
    unsavedChanges: "Есть несохраненные изменения, продолжить обновление?",
    confirmButton: "Подтвердить",
    cancelButton: "Отмена",
    alertTitle: "Подсказка",
    errorTitle: "Ошибка",
    noDataToConfirm: "Нет данных для подтверждения",
    confirmSuccess: "Обновление установленных значений успешно",
    confirmFailed: "Обновление установленных значений не удалось: ",
    responseLog: "Данные ответа:",
    continueAutoRefresh: "Продолжить включение автообновления",
    settingGroup: "Группа установленных значений",
    all: "Все",
    minValue: "Минимальное значение",
    maxValue: "Максимальное значение",
    step: "Шаг",
    unit: "Единица измерения",
    searchNamePlaceholder: "Введите название установленного значения для поиска",
    searchDescPlaceholder: "Введите описание установленного значения для поиска",
    autoRefreshWarning: "Изменение данных не разрешено при включенном автообновлении",
    invalidValue: "Значение {value} установленного значения {name} не находится в допустимом диапазоне",
    exportFileName: "Параметры устройства_Все установленные значения.xlsx",
    selectPathLog: "Выбрать путь: ",
    exportSuccess: "Экспорт списка установленных значений успешен"
  },
  variable: {
    autoRefresh: "Автообновление",
    variableName: "Имя переменной",
    inputVariableName: "Пожалуйста, введите имя переменной для добавления",
    refresh: "Обновить",
    add: "Добавить",
    confirm: "Подтвердить",
    import: "Импорт",
    export: "Экспорт",
    delete: "Удалить",
    noDataToConfirm: "Нет данных для подтверждения",
    warning: "Предупреждение",
    variableModifiedSuccess: "Изменение переменной успешно",
    variableModifiedFailed: "Изменение переменной не удалось, причина неудачи:",
    requestFailed: "Запрос не удался, пожалуйста, попробуйте позже",
    error: "Ошибка",
    success: "Успешно",
    variableAddSuccess: "Добавление переменной успешно",
    variableAddFailed: "Добавление переменной не удалось, причина неудачи:",
    variableDeleteSuccess: "Удаление переменной успешно",
    variableDeleteFailed: "Удаление переменной не удалось, причина неудачи:",
    exportSuccess: "Экспорт информации об отладочных переменных устройства успешен",
    exportFailed: "Экспорт информации об отладочных переменных устройства не удался, причина неудачи:",
    importSuccess: "Импорт информации об отладочных переменных устройства успешен",
    importFailed: "Импорт информации об отладочных переменных устройства не удался:",
    confirmRefresh: "Есть несохраненные изменения, продолжить обновление?",
    confirmAutoRefresh: "Есть несохраненные изменения, продолжить включение автообновления?",
    pleaseInputVariableName: "Пожалуйста, заполните имя переменной",
    exportTitle: "Экспорт отладочных переменных устройства",
    importTitle: "Импорт отладочных переменных устройства",
    defaultExportPath: "Отладочные переменные устройства.xlsx",
    title: "Отладка переменных",
    variableNamePlaceholder: "Пожалуйста, введите имя переменной для добавления",
    batchDelete: "Пакетное удаление",
    modifySuccess: "Изменение переменной успешно",
    modifyFailed: "Изменение переменной не удалось, причина неудачи: {msg}",
    alertTitle: "Предупреждение",
    successTitle: "Подсказка",
    confirmButton: "Подтвердить",
    cancelButton: "Отмена",
    sequence: "Номер",
    id: "ID",
    name: "Название",
    value: "Значение",
    type: "Тип",
    description: "Описание",
    address: "Адрес",
    operation: "Операция",
    enterVariableName: "Пожалуйста, введите имя переменной для добавления",
    responseLog: "Данные ответа:",
    addSuccess: "Добавление переменной успешно",
    addFailed: "Добавление переменной не удалось, причина неудачи:",
    addFailedWithName: "Добавление переменной {name} не удалось: {reason}",
    exportFileName: "Отладочные переменные устройства.xlsx",
    selectPathLog: "Выбрать путь:",
    exportSuccessLog: "Экспорт информации об отладочных переменных устройства успешен, {path}",
    exportFailedLog: "Экспорт информации об отладочных переменных устройства не удался, причина неудачи:",
    importFailedLog: "Импорт информации об отладочных переменных устройства не удался:",
    unsavedChanges: "Есть несохраненные изменения, продолжить обновление?",
    continueAutoRefresh: "Продолжить включение автообновления",
    tip: "Подсказка",
    sequenceNumber: "Номер",
    autoRefreshEditForbidden: "Редактирование запрещено в режиме автообновления",
    warningTitle: "Предупреждение",
    invalidNumber: "Недопустимое числовое значение: {value}",
    cancel: "Отмена"
  },
  backup: {
    sequence: "Номер",
    title: "Резервное копирование устройства",
    savePath: "Путь сохранения",
    setPath: "Установить путь сохранения резервной копии",
    setPathTitle: "Установить путь",
    startBackup: "Начать резервное копирование",
    cancelBackup: "Отменить резервное копирование",
    backup: "Резервное копирование",
    backupType: "Тип резервного копирования",
    progress: "Прогресс",
    status: "Статус",
    operation: "Операция",
    backupTypes: {
      paramValue: "Резервное копирование параметров установленных значений устройства",
      faultInfo: "Резервное копирование информации о неисправностях устройства",
      cidConfigPrjLog: "Резервное копирование cid/ccd/device_config/debug_info/prj/log",
      waveReport: "Резервное копирование файлов записи волн устройства"
    },
    backupDesc: "Описание содержимого резервной копии",
    backupDescTypes: {
      paramValue: "Экспорт установленных значений устройства (Экспорт установленных значений.xlsx)",
      faultInfo: "Экспорт информации о неисправностях устройства (отчеты о событиях/операциях/неисправностях/аудите)",
      cidConfigPrjLog: "Экспорт файлов конфигурации (CID/CCD, конфигурация XML, файлы журналов)",
      waveReport: "Экспорт файлов записи волн устройства (/wave/comtrade)"
    },
    locateFolder: "Найти папку",
    backupSuccess: "Резервное копирование успешно",
    backupFailed: "Резервное копирование не удалось",
    openFolderFailed: "Не удалось открыть папку",
    noTypeSelected: "Пожалуйста, сначала выберите тип резервного копирования",
    cancelSuccess: "Отмена успешна",
    cancelFailed: "Отмена не удалась",
    noBackupToCancel: "В настоящее время нет выполняющихся задач резервного копирования",
    noTaskIdFound: "ID задачи не найден, отмена невозможна",
    backupStatus: {
      starting: "Начало резервного копирования",
      userCancelled: "Отменено пользователем",
      transferring: "Передача"
    },
    console: {
      pathNotSet: "Путь резервного копирования не установлен, невозможно начать резервное копирование",
      noTypeSelected: "Тип резервного копирования не выбран, невозможно начать резервное копирование",
      startBackup: "Начало резервного копирования, тип: {types}, путь: {path}",
      backupException: "Исключение резервного копирования: {error}",
      pathSelected: "Выбран путь резервного копирования: {path}",
      pathNotSelected: "Путь резервного копирования не выбран",
      pathNotSetForLocate: "Путь резервного копирования не установлен, невозможно найти папку",
      folderOpened: "Открыта папка резервного копирования: {path}",
      openFolderFailed: "Не удалось открыть папку резервного копирования: {error}",
      taskCompleted: "Обработка задачи завершена",
      taskCancelled: "Задача отменена",
      typeError: "Ошибка типа [{type}]: {error}",
      typeCompleted: "Резервное копирование типа [{type}] завершено",
      typeCancelled: "Тип [{type}] отменен",
      typeFailed: "Тип [{type}] не удался",
      attemptCancel: "Попытка отменить задачу резервного копирования",
      noTaskIdFound: "ID задачи не найден, невозможно отменить резервное копирование",
      cancelSuccess: "Задача резервного копирования отменена",
      cancelFailed: "Отмена резервного копирования не удалась: {error}",
      cancelException: "Исключение отмены резервного копирования: {error}",
      singleCancelSuccess: "Отмена типа [{type}] успешна",
      singleCancelFailed: "Отмена типа [{type}] не удалась: {error}",
      singleCancelException: "Исключение отмены типа [{type}]: {error}"
    }
  },
  operate: {
    title: "Операции устройства",
    manualWave: "Ручная запись волн",
    resetDevice: "Сброс устройства",
    clearReport: "Очистить отчеты",
    clearWave: "Очистить записи волн",
    executing: "Выполнение...",
    selectOperation: "Пожалуйста, выберите операцию",
    success: {
      manualWave: "Ручная запись волн успешна",
      resetDevice: "Сброс устройства успешен",
      clearReport: "Очистка отчетов успешна",
      clearWave: "Очистка записей волн успешна"
    },
    fail: {
      manualWave: "Ручная запись волн не удалась, причина неудачи:",
      resetDevice: "Сброс устройства не удался, причина неудачи:",
      clearReport: "Очистка отчетов не удалась, причина неудачи:",
      clearWave: "Очистка записей волн не удалась, причина неудачи:"
    }
  },
  time: {
    title: "Синхронизация времени устройства",
    currentTime: "Текущее время",
    deviceTime: "Время устройства",
    selectDateTime: "Выбрать дату и время",
    milliseconds: "Миллисекунды",
    now: "Сейчас",
    read: "Читать",
    write: "Записать",
    readSuccess: "Чтение времени устройства успешно.",
    readFailed: "Чтение времени устройства не удалось: {msg}",
    readFailedInvalidFormat: "Чтение времени устройства не удалось: недопустимый формат времени",
    readFailedDataError: "Чтение времени устройства не удалось: ошибка формата данных времени",
    writeSuccess: "Запись времени устройства успешна.",
    writeFailed: "Запись времени устройства не удалась: {msg}",
    writeFailedInvalidFormat: "Запись времени устройства не удалась: недопустимый формат времени",
    millisecondsRangeError: "Диапазон значений миллисекунд должен быть между 0-999",
    unknownError: "Неизвестная ошибка"
  },
  reportOperate: {
    title: "Операции с отчетами",
    date: "Дата:",
    search: "Запрос",
    save: "Сохранить",
    clearList: "Очистить список",
    loading: "Загрузка данных",
    progress: {
      title: "Информация о прогрессе",
      loading: "Загрузка",
      searching: "Запрос {type}"
    },
    table: {
      reportId: "Номер отчета",
      name: "Название",
      time: "Время",
      operationAddress: "Адрес операции",
      operationParam: "Параметр операции",
      value: "Значение",
      step: "Шаг",
      source: "Источник",
      sourceType: "Тип источника",
      result: "Результат"
    },
    messages: {
      selectDateRange: "Пожалуйста, выберите полный временной диапазон",
      noDataToSave: "Нет данных для сохранения",
      saveSuccess: "Сохранение успешно",
      saveReport: "Сохранить отчет"
    }
  },
  reportGroup: {
    title: "Группа отчетов",
    date: "Дата:",
    search: "Запрос",
    save: "Сохранить",
    clearList: "Очистить список",
    autoRefresh: "Автообновление",
    loading: "Загрузка данных",
    progress: {
      title: "Информация о прогрессе",
      loading: "Загрузка",
      searching: "Запрос {type}"
    },
    table: {
      reportId: "Номер отчета",
      time: "Время",
      description: "Описание"
    },
    contextMenu: {
      uploadWave: "Вызвать запись волн",
      getHistoryReport: "Получить исторический отчет",
      saveResult: "Сохранить результат",
      clearContent: "Очистить содержимое страницы"
    },
    messages: {
      selectDateRange: "Пожалуйста, выберите полный временной диапазон",
      noDataToSave: "Нет данных для сохранения",
      noFileToUpload: "Нет файлов для вызова",
      saveSuccess: "Сохранение успешно",
      saveReport: "Сохранить отчет",
      waveToolNotConfigured: "Путь к стороннему инструменту анализа волн не настроен",
      waveFileUploading: "Вызов файла записи волн",
      waveFileUploadComplete: "Вызов файла записи волн завершен",
      waveFileUploadCompleteWithPath: "Вызов файла записи волн завершен, путь: {path}",
      openWaveFileConfirm: "Открыть файл волн через сторонний инструмент?",
      openWaveFileTitle: "Дружеское напоминание",
      confirm: "Подтвердить",
      cancel: "Отмена"
    },
    refresh: {
      stop: "Остановить обновление",
      start: "Автообновление"
    },
    hiddenItems: {
      show: "Показать скрытые элементы",
      hide: "Не показывать скрытые элементы"
    }
  },
  fileDownload: {
    title: "Скачивание файлов",
    deviceDirectory: "Каталог устройства",
    reboot: "Перезагрузить",
    noReboot: "Не перезагружать",
    selectFile: "Выбрать файл",
    addDownloadFile: "Добавить файл для скачивания",
    addDownloadFolder: "Добавить папку для скачивания",
    addDownloadFilesAndFolders: "Добавить файлы и папки",
    downloadFile: "Скачать файл",
    cancelDownload: "Отменить скачивание",
    importList: "Импорт списка",
    exportList: "Экспорт списка",
    batchDelete: "Пакетное удаление",
    clearList: "Очистить список",
    download: "Скачать",
    delete: "Удалить",
    fileName: "Название файла",
    fileSize: "Размер файла",
    filePath: "Путь к файлу",
    lastModified: "Время последнего изменения",
    progress: "Прогресс",
    status: "Статус",
    operation: "Операция",
    folder: "Папка",
    waitingDownload: "Ожидание скачивания",
    calculatingFileInfo: "Вычисление информации о файле",
    downloadPreparing: "Подготовка к скачиванию",
    downloading: "Скачивание......",
    downloadComplete: "Скачивание завершено",
    downloadError: "Ошибка скачивания:",
    userCancelled: "Отменено пользователем",
    allFilesComplete: "Скачивание завершено",
    fileExists: "Файл {path} уже существует, добавление не удалось!",
    selectValidFile: "Пожалуйста, выберите допустимый файл для операции скачивания",
    remotePathEmpty: "Удаленный путь не может быть пустым",
    noDownloadTask: "Не получена задача скачивания, отмена невозможна",
    fileSizeZero: "Файл {fileName} имеет размер 0, скачивание невозможно",
    downloadCancelled: "Отмена скачивания файла {path} завершена",
    downloadCancelledFailed: "Отмена скачивания файла {path} не удалась, причина неудачи: {msg}",
    fileDeleted: "Удаление файла {path} завершено",
    exportSuccess: "Экспорт списка файлов для скачивания успешен",
    exportFailed: "Экспорт списка файлов для скачивания не удался",
    importSuccess: "Импорт списка файлов для скачивания успешен",
    importFailed: "Импорт списка файлов для скачивания не удался: {msg}",
    downloadList: "Список файлов для скачивания",
    exportTitle: "Экспорт списка файлов для скачивания",
    importTitle: "Импорт списка файлов для скачивания",
    error: "Ошибка",
    tip: "Подсказка",
    confirm: "Подтвердить",
    sequence: "Номер",
    confirmButton: "Подтвердить",
    cancelButton: "Отмена",
    alertTitle: "Подсказка",
    errorTitle: "Ошибка",
    successTitle: "Успешно",
    warningTitle: "Предупреждение",
    loading: "Загрузка",
    executing: "Выполнение...",
    noData: "Нет данных",
    selectDateRange: "Пожалуйста, выберите диапазон дат",
    search: "Поиск",
    save: "Сохранить",
    clear: "Очистить",
    refresh: "Обновить",
    stop: "Остановить",
    start: "Начать",
    show: "Показать",
    hide: "Скрыть",
    showHiddenItems: "Показать скрытые элементы",
    hideHiddenItems: "Скрыть элементы",
    continue: "Продолжить",
    cancel: "Отмена",
    confirmImport: "Подтвердить импорт",
    confirmExport: "Подтвердить экспорт",
    confirmDelete: "Подтвердить удаление",
    confirmClear: "Подтвердить очистку",
    confirmCancel: "Подтвердить отмену",
    confirmContinue: "Подтвердить продолжение",
    confirmStop: "Подтвердить остановку",
    confirmStart: "Подтвердить запуск",
    confirmShow: "Подтвердить показ",
    confirmHide: "Подтвердить скрытие",
    confirmRefresh: "Подтвердить обновление",
    confirmSave: "Подтвердить сохранение",
    confirmSearch: "Подтвердить поиск",
    confirmClearList: "Подтвердить очистку списка",
    confirmImportList: "Подтвердить импорт списка",
    confirmExportList: "Подтвердить экспорт списка",
    confirmBatchDelete: "Подтвердить пакетное удаление",
    confirmDownload: "Подтвердить скачивание",
    confirmCancelDownload: "Подтвердить отмену скачивания",
    confirmDeleteFile: "Подтвердить удаление файла",
    confirmClearFiles: "Подтвердить очистку файлов",
    confirmImportFiles: "Подтвердить импорт файлов",
    confirmExportFiles: "Подтвердить экспорт файлов",
    confirmBatchDeleteFiles: "Подтвердить пакетное удаление файлов",
    confirmDownloadFiles: "Подтвердить скачивание файлов",
    confirmCancelDownloadFiles: "Подтвердить отмену скачивания файлов",
    rename: "Переименование при скачивании",
    renamePlaceholder: "Переименовать при скачивании (необязательно)",
    renameCopyFailed: "Переименование копии файла не удалось:",
    packageProgram: "Упаковка программы",
    selectSaveDir: "Выбрать каталог сохранения",
    packageBtn: "Упаковать",
    locateDir: "Найти папку",
    saveDirEmpty: "Пожалуйста, сначала выберите каталог сохранения!",
    packageSuccess: "Упаковка программы завершена!",
    packageFailed: "Упаковка не удалась: {msg}",
    noFileSelected: "Пожалуйста, сначала выберите файлы для упаковки!",
    zipPath: "Путь упаковки: {zipPath}",
    addToDownload: "Добавить в интерфейс скачивания",
    fileAdded: "Файл добавлен в список скачивания: {path}",
    rebootSuccess: "Перезагрузка устройства успешна",
    rebootFailed: "Перезагрузка устройства не удалась: {msg}"
  },
  fileUpload: {
    serialNumber: "Номер",
    title: "Загрузка файлов",
    importList: "Импорт списка",
    exportList: "Экспорт списка",
    batchDelete: "Пакетное удаление",
    clearList: "Очистить список",
    upload: "Загрузить",
    cancelUpload: "Отменить загрузку",
    delete: "Удалить",
    sequence: "Номер",
    fileName: "Название файла",
    fileSize: "Размер файла",
    filePath: "Путь к файлу",
    lastModified: "Время последнего изменения",
    progress: "Прогресс",
    statusTitle: "Статус",
    status: {
      waiting: "Ожидание загрузки",
      preparing: "Подготовка к загрузке",
      uploading: "Загрузка......",
      completed: "Загрузка завершена",
      error: "Ошибка загрузки:",
      cancelled: "Отменено пользователем"
    },
    operation: "Операция",
    calculatingFileInfo: "Вычисление информации о файле",
    uploadPreparing: "Подготовка к загрузке",
    uploading: "Загрузка......",
    uploadComplete: "Загрузка завершена",
    uploadError: "Ошибка загрузки: {errorMsg}",
    userCancelled: "Отменено пользователем",
    allFilesComplete: "Загрузка завершена",
    fileExists: "Файл {path} уже существует, добавление не удалось!",
    invalidFile: "Пожалуйста, выберите допустимый файл для операции загрузки",
    emptySavePath: "Путь сохранения файла не может быть пустым",
    fileUploadComplete: "Загрузка файла {fileName} завершена",
    selectPath: "Выбрать путь",
    pathOptions: {
      shr: "/shr",
      configuration: "/shr/configuration",
      log: "/log",
      wave: "/wave",
      comtrade: "/wave/comtrade"
    },
    deviceDirectory: "Каталог устройства",
    savePath: "Путь сохранения",
    setPath: "Установить путь",
    getFiles: "Получить файлы",
    uploadFiles: "Загрузить файлы",
    errors: {
      invalidFile: "Пожалуйста, выберите допустимый файл для операции загрузки",
      emptySavePath: "Путь сохранения файла не может быть пустым",
      noUploadTask: "Не получена задача загрузки, отмена невозможна",
      getFilesFailed: "Не удалось получить файлы каталога устройства",
      fileSizeZero: "Файл {fileName} имеет размер 0, загрузка невозможна"
    },
    messages: {
      uploadCompleted: "Загрузка файла завершена",
      uploadCancelled: "Отмена загрузки файла завершена",
      clearListSuccess: "Очистка списка файлов успешна"
    }
  },
  info: {
    title: "Информация об устройстве",
    export: "Экспорт",
    exportSuccess: "Экспорт основной информации об устройстве успешен",
    exportFailed: "Экспорт основной информации об устройстве не удался",
    exportTip: "Подсказка",
    confirm: "Подтвердить",
    exportLoading: "Экспорт основной информации об устройстве...",
    getInfoFailed: "Получение информации об устройстве не удалось. Причина неудачи:",
    dataEmpty: "Данные пусты!"
  },
  summary: {
    title: "Обзор группировки устройств",
    basicInfo: "Основная информация",
    settingTotal: "Общее количество установленных значений",
    telemetry: "Телеизмерения",
    teleindication: "Телесигнализация",
    telecontrol: "Телеуправление",
    driveOutput: "Выходная передача",
    settingRatio: "Соотношение установленных значений"
  },
  dict: {
    refresh: "Обновить",
    confirm: "Подтвердить",
    import: "Импорт",
    export: "Экспорт",
    sequence: "Номер",
    shortAddress: "Короткий адрес",
    shortAddressTooltip: "Введите короткий адрес для поиска",
    chinese: "Китайский",
    english: "Английский",
    spanish: "Испанский",
    french: "Французский",
    russian: "Русский",
    operation: "Операция",
    confirmLog: "Подтвердить словарь",
    importLog: "Импорт словаря",
    exportLog: "Экспорт словаря",
    refreshLog: "Обновить словарь",
    newValueLog: "Новое значение:",
    loadSuccess: "Загрузка записей успешна",
    loadFailed: "Загрузка записей не удалась",
    saveSuccess: "Сохранение записей успешно",
    saveFailed: "Сохранение записей не удалось",
    partialFailed: "Сохранение части записей не удалось",
    noChanges: "Нет измененных записей",
    confirmMessage: "Вы уверены, что хотите сохранить измененные записи?"
  },
  allParamCompare: {
    title: "Сравнение различий импорта всех установленных значений",
    cancel: "Отмена",
    confirm: "Подтвердить импорт",
    groupName: "Название группы",
    name: "Название",
    description: "Описание",
    minValue: "Минимальное значение",
    maxValue: "Максимальное значение",
    step: "Шаг",
    unit: "Единица измерения",
    address: "Адрес",
    oldValue: "Старое значение",
    newValue: "Новое значение",
    sequence: "Номер",
    searchName: "Введите название установленного значения для поиска",
    searchDescription: "Введите описание установленного значения для поиска",
    messages: {
      noSelection: "Не выбраны данные",
      error: "Ошибка"
    }
  },
  error: {
    formInitFailed: "Ошибка инициализации формы, пожалуйста, повторите попытку",
    enterMenuName: "Пожалуйста, введите название меню",
    enterMenuDesc: "Пожалуйста, введите описание меню",
    completeForm: "Пожалуйста, заполните форму полностью",
    editFailed: "Ошибка редактирования: {msg}",
    createFailed: "Ошибка создания: {msg}",
    deleteFailed: "Ошибка удаления: {msg}",
    enterReportName: "Пожалуйста, введите название отчета",
    enterReportDesc: "Пожалуйста, введите описание отчета",
    selectInherit: "Пожалуйста, выберите наследуемое поле",
    incompleteData: "Неполные данные, проверьте название и описание группы",
    selectAtLeastOnePoint: "Пожалуйста, выберите хотя бы одну точку",
    createPointGroupUnderCustomMenu: "Пользовательские группы точек можно создавать только под узлом пользовательского меню",
    createFailedRetry: "Ошибка создания, пожалуйста, повторите попытку",
    editModeOnlyForPointGroup: "Режим редактирования доступен только для узлов групп точек",
    parentMenuNotFound: "Информация о родительском меню не найдена",
    editFailedRetry: "Ошибка редактирования, пожалуйста, повторите попытку",
    operationFailed: "Ошибка операции: {msg}"
  },
  deviceForm: {
    title: {
      add: "Добавить устройство",
      edit: "Редактировать устройство"
    },
    name: "Название устройства",
    ip: "IP-адрес",
    port: "Порт",
    connectTimeout: "Время ожидания подключения (мс)",
    readTimeout: "Глобальное время ожидания запроса (мс)",
    paramTimeout: "Время ожидания изменения установленного значения (мс)",
    encrypted: "Зашифрованное соединение",
    isVirtual: "Виртуальное устройство",
    advanced: {
      show: "Развернуть дополнительные опции",
      hide: "Свернуть дополнительные опции"
    },
    buttons: {
      cancel: "Отмена",
      confirm: "Подтвердить"
    },
    messages: {
      nameRequired: "Пожалуйста, введите название устройства",
      nameTooLong: "Название устройства не должно быть слишком длинным",
      invalidIp: "Пожалуйста, введите действительный IP-адрес",
      invalidPort: "Номер порта должен быть между 1-65535",
      timeoutTooShort: "Время ожидания не должно быть слишком коротким"
    }
  },
  paramCompare: {
    title: "Сравнение различий импорта установленных значений",
    cancel: "Отмена",
    confirm: "Подтвердить импорт",
    sequence: "Номер",
    name: "Название",
    description: "Описание",
    minValue: "Минимальное значение",
    maxValue: "Максимальное значение",
    step: "Шаг",
    unit: "Единица измерения",
    address: "Адрес",
    oldValue: "Старое значение",
    newValue: "Новое значение",
    searchName: "Введите название установленного значения для поиска",
    searchDescription: "Введите описание установленного значения для поиска",
    messages: {
      noSelection: "Не выбраны данные",
      error: "Ошибка"
    }
  },
  progress: {
    title: "Информация о прогрессе",
    executing: "Выполнение..."
  },
  remoteYm: {
    title: "Дистанционные импульсы",
    sequence: "Номер",
    shortAddress: "Короткий адрес",
    description: "Описание",
    value: "Значение",
    operation: "Операция",
    inputShortAddressFilter: "Введите фильтр по короткому адресу",
    inputDescriptionFilter: "Введите фильтр по описанию",
    invalidData: "Значение {value} данных {name} недопустимо",
    error: "Ошибка",
    success: "Успешно",
    executeSuccess: "Выполнение успешно",
    prompt: "Подсказка",
    confirmButton: "Подтвердить",
    confirmExecute: "Подтвердить выполнение",
    confirm: "Подтвердить",
    executeButton: "Выполнить",
    cancelButton: "Отмена"
  },
  remoteYt: {
    title: "Дистанционное регулирование",
    sequence: "Номер",
    directControl: "Прямое управление",
    selectControl: "Селективное управление",
    shortAddress: "Короткий адрес",
    description: "Описание",
    value: "Значение",
    operation: "Операция",
    inputShortAddressFilter: "Введите фильтр по короткому адресу",
    inputDescriptionFilter: "Введите фильтр по описанию",
    invalidData: "Значение {value} данных {name} недопустимо",
    error: "Ошибка",
    success: "Успешно",
    executeSuccess: "Выполнение успешно",
    prompt: "Подсказка",
    confirm: "Подтвердить",
    errorInfo: "Информация об ошибке",
    executeFailed: "Выполнение дистанционного регулирования не удалось, причина неудачи: {msg}",
    executeSuccessLog: "Дистанционное регулирование {desc} выполнено успешно",
    cancelSuccess: "Отмена успешна",
    cancelFailed: "Отмена дистанционного регулирования не удалась, причина неудачи: {msg}",
    selectSuccess: "Выбор успешен, выполнить?",
    confirmInfo: "Информация подтверждения",
    execute: "Выполнить",
    cancel: "Отмена"
  },
  paramSetting: {
    title: "Параметры установленных значений устройства",
    queryFailed: "Ошибка запроса значений: {msg}",
    autoRefresh: "Автообновление",
    refresh: "Обновить",
    confirm: "Подтвердить",
    import: "Импорт",
    export: "Экспорт",
    currentEditArea: "Текущая рабочая область",
    selectEditArea: "Текущая область редактирования",
    noDataToImport: "Нет данных для импорта",
    noDataToConfirm: "Нет данных для подтверждения",
    importSuccess: "Импорт установленных значений успешен",
    importFailed: "Импорт установленных значений не удался",
    updateSuccess: "Обновление установленных значений успешно",
    updateFailed: "Обновление установленных значений не удалось",
    requestFailed: "Запрос не удался, пожалуйста, попробуйте позже",
    setEditArea: "Настроить",
    setEditAreaTitle: "Настроить область редактирования",
    setEditAreaSuccess: "Настройка области редактирования успешна",
    modifiedWarning: "Есть несохраненные изменения, продолжить обновление?",
    autoRefreshWarning: "Есть несохраненные изменения, продолжить включение автообновления?",
    autoRefreshDisabled: "Изменение данных не разрешено при включенном автообновлении",
    invalidValue: "Значение {value} установленного значения {name} не находится в допустимом диапазоне",
    exportSuccess: "Экспорт параметров установленных значений устройства успешен",
    exportFailed: "Экспорт параметров установленных значений устройства не удался",
    noDiffData: "Не получены данные различий",
    table: {
      index: "Номер",
      name: "Название",
      description: "Описание",
      value: "Значение",
      minValue: "Минимальное значение",
      maxValue: "Максимальное значение",
      step: "Шаг",
      address: "Адрес",
      unit: "Единица измерения",
      operation: "Операция"
    },
    search: {
      namePlaceholder: "Введите название установленного значения для поиска",
      descPlaceholder: "Введите описание установленного значения для поиска"
    }
  },
  remoteControl: {
    title: "Дистанционное управление",
    sequence: "Номер",
    shortAddress: "Короткий адрес",
    description: "Описание",
    control: "Управление размыканием/замыканием",
    type: "Тип",
    operation: "Операция",
    directControl: "Прямое управление",
    selectControl: "Селективное управление",
    controlClose: "Управление размыканием",
    controlOpen: "Управление замыканием",
    noCheck: "Без проверки",
    syncCheck: "Проверка синхронизации",
    deadCheck: "Проверка отсутствия напряжения",
    confirmInfo: "Информация подтверждения",
    execute: "Выполнить",
    cancel: "Отмена",
    confirm: "Подтвердить",
    success: "Успешно",
    failed: "Не удалось",
    errorInfo: "Информация об ошибке",
    promptInfo: "Информация подсказки",
    confirmSuccess: "Выбор успешен, выполнить?",
    executeSuccess: "Выполнение успешно",
    cancelSuccess: "Отмена успешна",
    executeFailed: "Выполнение дистанционного управления не удалось, причина неудачи:",
    cancelFailed: "Отмена дистанционного управления не удалась, причина неудачи:",
    remoteExecuteSuccess: "Выполнение дистанционного управления успешно",
    remoteCancelSuccess: "Отмена дистанционного управления успешна"
  },
  remoteDrive: {
    action: "Действие",
    executeSuccess: "Выполнение успешно",
    executeFailed: "Выполнение не удалось",
    prompt: "Информация подсказки",
    error: "Информация об ошибке",
    confirm: "Подтвердить",
    shortAddress: "Короткий адрес",
    description: "Описание",
    operation: "Операция",
    enterToFilter: "Введите фильтр по короткому адресу",
    enterToFilterDesc: "Введите фильтр по описанию",
    actionSuccess: "Выполнение действия успешно",
    actionFailed: "Выполнение действия не удалось",
    failureReason: "Причина неудачи",
    sequence: "Номер"
  },
  remoteSignal: {
    autoRefresh: "Автообновление",
    refresh: "Обновить",
    export: "Экспорт",
    sequence: "Номер",
    name: "Название",
    description: "Описание",
    value: "Значение",
    quality: "Качество",
    searchName: "Введите название для поиска",
    searchDesc: "Введите описание для поиска",
    searchValue: "Введите значение для поиска",
    exportTitle: "Экспорт информации о телесигнализации устройства",
    exportSuccess: "Экспорт информации о телесигнализации устройства успешен",
    exportFailed: "Экспорт информации о телесигнализации устройства не удался",
    exportSuccessWithPath: "Экспорт информации о телесигнализации устройства успешен,",
    exportFailedWithError: "Экспорт информации о телесигнализации устройства не удался:",
    invalidData: "Недопустимые данные:",
    errorInDataCallback: "Ошибка обработки обратного вызова данных:",
    errorFetchingData: "Ошибка при получении данных:"
  },
  remoteTelemetry: {
    autoRefresh: "Автообновление",
    refresh: "Обновить",
    export: "Экспорт",
    sequence: "Номер",
    name: "Название",
    description: "Описание",
    value: "Значение",
    unit: "Единица измерения",
    quality: "Качество",
    searchName: "Введите название для поиска",
    searchDesc: "Введите описание для поиска",
    searchValue: "Введите значение для поиска",
    exportTitle: "Экспорт информации о состоянии устройства",
    exportSuccess: "Экспорт информации о состоянии устройства успешен",
    exportFailed: "Экспорт информации о состоянии устройства не удался",
    exportSuccessWithPath: "Экспорт информации о состоянии устройства успешен,",
    exportFailedWithError: "Экспорт информации о состоянии устройства не удался:",
    confirm: "Подтвердить",
    tip: "Подсказка",
    exportFileName: "Информация о состоянии устройства",
    selectPathLog: "Выбрать путь:"
  },
  remote: {
    directControl: "Прямое управление",
    selectControl: "Селективное управление",
    serialNumber: "Номер",
    shortAddress: "Короткий адрес",
    description: "Описание",
    value: "Значение",
    operation: "Операция",
    inputShortAddressFilter: "Введите фильтр по короткому адресу",
    inputDescriptionFilter: "Введите фильтр по описанию",
    invalidData: "Значение {value} данных {name} недопустимо",
    error: "Ошибка",
    success: "Успешно",
    executeSuccess: "Выполнение успешно",
    prompt: "Информация подсказки",
    confirm: "Подтвердить",
    errorInfo: "Информация об ошибке",
    executeFailed: "Выполнение дистанционного регулирования не удалось, причина неудачи: {msg}",
    executeSuccessLog: "Дистанционное регулирование {desc} выполнено успешно",
    cancelSuccess: "Отмена успешна",
    cancelFailed: "Отмена дистанционного регулирования не удалась, причина неудачи: {msg}",
    selectSuccess: "Выбор успешен, выполнить?",
    confirmInfo: "Информация подтверждения",
    execute: "Выполнить",
    cancel: "Отмена"
  },
  report: {
    uploadWave: "Вызвать запись волн",
    searchHistory: "Получить исторический отчет",
    saveResult: "Сохранить результат",
    clearContent: "Очистить содержимое страницы",
    date: "Дата",
    query: "Запрос",
    save: "Сохранить",
    autoRefresh: "Автообновление",
    stopRefresh: "Остановить обновление",
    clearList: "Очистить список",
    progressInfo: "Информация о прогрессе",
    loading: "Загрузка данных",
    reportNo: "Номер отчета",
    time: "Время",
    description: "Описание",
    noFileToUpload: "Нет файлов для вызова",
    uploadSuccess: "Вызов файла записи волн завершен",
    uploadPath: "Вызов файла записи волн завершен, путь:",
    noDataToSave: "Нет данных для сохранения",
    saveSuccess: "Сохранение успешно",
    saveReport: "Сохранить отчет",
    openWaveConfirm: "Открыть файл волн через сторонний инструмент?",
    confirm: "Подтвердить",
    cancel: "Отмена",
    waveToolNotConfigured: "Путь к стороннему инструменту анализа волн не настроен",
    pleaseSelectTimeRange: "Пожалуйста, выберите полный временной диапазон",
    querying: "Выполняется запрос",
    reportNumber: "Номер отчета",
    operationAddress: "Адрес операции",
    operationParams: "Параметры операции",
    result: "Результат",
    progress: "Информация о прогрессе",
    loadingText: "Загрузка",
    selectCompleteTimeRange: "Пожалуйста, выберите полный временной диапазон",
    fileUploading: "Вызов файла записи волн",
    fileUploadComplete: "Вызов файла завершен"
  },
  customMenu: {
    addMenu: "Создать пользовательское меню",
    editMenu: "Редактировать пользовательское меню",
    deleteMenu: "Удалить пользовательское меню",
    addReport: "Создать пользовательский отчет",
    editReport: "Редактировать пользовательский отчет",
    deleteReport: "Удалить пользовательский отчет",
    addPointGroup: "Создать пользовательскую группу",
    editPointGroup: "Редактировать пользовательскую группу",
    deletePointGroup: "Удалить пользовательскую группу",
    selectedPoints: "Выбранные точки",
    selectFc: "Выбрать FC",
    selectGroupType: "Выбрать тип группы",
    groupTypes: {
      ST: "Телесигнализация",
      MX: "Телеизмерения",
      SP: "Установленные значения одной зоны",
      SG: "Установленные значения нескольких зон"
    },
    filterPlaceholder: "Фильтр по названию/описанию",
    loadingData: "Загрузка данных...",
    noDataForFc: "Нет данных для этого FC",
    noDataForGroupType: "Нет данных для этого типа группы",
    pleaseSelectFc: "Пожалуйста, сначала выберите FC",
    pleaseSelectGroupType: "Пожалуйста, сначала выберите тип группы",
    loadingGroupTypeData: "Получение данных типа группы...",
    loadingGroupTypes: "Загрузка данных типов групп...",
    loadedGroupTypes: "Загружены типы групп",
    dataLoadComplete: "Загрузка данных завершена",
    dataLoadFailed: "Загрузка данных не удалась",
    switchingToGroupType: "Переключение на",
    loadingGroupTypeDataSingle: "Загрузка данных...",
    loadGroupTypeFailed: "Загрузка данных не удалась",
    loadGroupTypeError: "Произошла ошибка при загрузке данных",
    inputGroupName: "Пожалуйста, введите название группы",
    inputGroupDesc: "Пожалуйста, введите описание группы",
    selectGroupTypeFirst: "Пожалуйста, сначала выберите тип группы",
    nameValidationFailed: "Проверка названия не удалась, пожалуйста, попробуйте снова",
    nameConflictWithSystem: "Название конфликтует с системным меню, пожалуйста, используйте другое название",
    nameConflictWithCustom: "Название конфликтует с существующим пользовательским меню, пожалуйста, используйте другое название",
    refreshData: "Обновить данные",
    menuName: "Название группы",
    menuDesc: "Описание",
    reportName: "Название отчета",
    reportDesc: "Описание",
    reportKeyword: "Ключевое слово",
    reportInherit: "Наследовать отчет",
    inputMenuName: "Пожалуйста, введите название группы",
    inputMenuDesc: "Пожалуйста, введите описание",
    inputReportName: "Пожалуйста, введите название отчета",
    inputReportDesc: "Пожалуйста, введите описание",
    inputReportKeyword: "Пожалуйста, введите ключевое слово",
    selectReportInherit: "Пожалуйста, выберите наследуемый отчет",
    cancel: "Отмена",
    confirm: "Подтвердить",
    successAddMenu: "Создание пользовательского меню успешно",
    successEditMenu: "Редактирование пользовательского меню успешно",
    successDeleteMenu: "Удаление пользовательского меню успешно",
    successAddReport: "Создание пользовательского отчета успешно",
    successEditReport: "Редактирование пользовательского отчета успешно",
    successDeleteReport: "Удаление пользовательского отчета успешно",
    successDeletePointGroup: "Удаление пользовательской группы успешно",
    errorAction: "Операция не удалась",
    errorDelete: "Удаление не удалось",
    confirmDeleteMenu: "Вы уверены, что хотите удалить это пользовательское меню?",
    confirmDeleteReport: "Вы уверены, что хотите удалить этот пользовательский отчет?",
    confirmDeletePointGroup: "Вы уверены, что хотите удалить эту пользовательскую группу?",
    tip: "Подсказка"
  },
  tree: {
    inputGroupName: "Пожалуйста, введите название группы",
    expandAll: "Развернуть все",
    collapseAll: "Свернуть все"
  }
};
