/**
 * Electron 俄语翻译
 * <AUTHOR> Assistant
 */

import errors from "./ru/errors";
import logs from "./ru/logs";
import common from "./ru/common";
import dialogs from "./ru/dialogs";
import systemFolders from "./ru/systemFolders";
import deviceOperations from "./ru/deviceOperations";
import configuration from "./ru/configuration";
import fileOperations from "./ru/fileOperations";
import tray from "./ru/tray";
import deviceInfo from "./ru/deviceInfo";
import reports from "./ru/reports";
import params from "./ru/params";
import services from "./ru/services";
import backup from "./ru/backup";
import deviceFile from "./ru/deviceFile";
import variable from "./ru/variable";
import matrixJob from "./ru/matrixJob";
import hmi from "./ru/hmi";
import remoteControl from "./ru/remoteControl";
import remoteYxAndYc from "./ru/remoteYxAndYc";
import virtualDevice from "./ru/virtualDevice";

export default {
  errors,
  logs,
  common,
  dialogs,
  systemFolders,
  deviceOperations,
  configuration,
  fileOperations,
  tray,
  deviceInfo,
  reports,
  params,
  services,
  backup,
  deviceFile,
  variable,
  matrixJob,
  hmi,
  remoteControl,
  remoteYxAndYc,
  virtualDevice,
};
